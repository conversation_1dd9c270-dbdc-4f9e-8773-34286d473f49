<!-- Header Component -->
<header class="bg-white shadow-md">
    <div class="container mx-auto px-4 py-2">
        <div class="flex justify-between items-center">           
            <div class="flex items-center">
                <img src="./img/logoPN.png" alt="Logo Nhà Hàng" class="h-16 mr-4 rounded-md border border-gray-300">
                <div class="text-2xl font-bold text-primary mr-2">
                    <span class="font-cursive">Phương Nam</span>
                </div>
                <div class="text-sm">
                    <div>Ẩm Thực</div>
                </div>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex space-x-8">
                <a href="Index-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="Index">Trang Chủ</a>
                <a href="gioithieu-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="gioithieu">Gi<PERSON><PERSON></a>
                <a href="Menu-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="Menu">Thực Đơn</a>
                <a href="lienhe&datban-new.html" class="nav-link text-gray-700 hover:text-primary transition duration-300 font-medium" data-page="lienhe&datban">Đặt Bàn & Liên Hệ</a>
            </nav>

            <div class="flex items-center">
                <div id="userDisplay" class="hidden mr-4">
                    <span class="text-gray-700 mr-2">Xin chào,</span>
                    <span id="userName" class="font-semibold text-primary"></span>
                </div>



                <!-- Invoice History Button - Only visible when logged in -->
                <button id="invoiceHistoryBtn" class="hidden bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary hover:text-white transition duration-300 mr-3" onclick="window.location.href='DanhSachHoaDon.html'">
                    <i class="fas fa-receipt mr-2"></i>Lịch sử đơn hàng
                </button>



                <button id="loginBtn" class="md:block bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary transition duration-300 mr-3">
                    Đăng Nhập
                </button>
                <button id="logoutBtn" class="hidden bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary  transition duration-300 mr-3">
                    Đăng Xuất
                </button>

                <!-- Cart Button - Only visible on menu page -->
                <button id="cartBtn" class="hidden relative mr-3 p-2 rounded-lg hover:bg-gray-100 transition duration-300 group">
                    <i class="fas fa-shopping-cart text-gray-600 group-hover:text-primary text-xl transition duration-300"></i>
                    <span id="cartCounter" class="absolute -top-1 -right-1 bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold shadow-lg transform scale-0 transition-transform duration-300">0</span>
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                        Giỏ hàng
                    </div>
                </button>
                
                <!-- Nút đổi ngôn ngữ -->
                <button id="language-toggle" class="bg-white text-yellow-800 px-4 py-2 rounded-lg border border-yellow-500 hover:bg-yellow-100 transition duration-300 mr-3">
                    EN
                </button>

                <button id="menuToggle" class="md:hidden ml-4 focus:outline-none">
                    <i class="fas fa-bars text-2xl text-gray-600"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <nav id="mobileNav" class="md:hidden hidden mt-4 pb-4">
            <div class="flex flex-col">
                <!-- Main Navigation Links -->
                <div class="space-y-1 mb-4">
                    <a href="Index-new.html" class="mobile-nav-link flex items-center py-3 px-4 rounded-lg hover:bg-gray-100 transition duration-300" data-page="Index">
                        <i class="fas fa-home w-5 mr-3 text-gray-600"></i>
                        <span class="font-medium">Trang Chủ</span>
                    </a>
                    <a href="gioithieu-new.html" class="mobile-nav-link flex items-center py-3 px-4 rounded-lg hover:bg-gray-100 transition duration-300" data-page="gioithieu">
                        <i class="fas fa-info-circle w-5 mr-3 text-gray-600"></i>
                        <span class="font-medium">Giới Thiệu</span>
                    </a>
                    <a href="Menu-new.html" class="mobile-nav-link flex items-center py-3 px-4 rounded-lg hover:bg-gray-100 transition duration-300" data-page="Menu">
                        <i class="fas fa-utensils w-5 mr-3 text-gray-600"></i>
                        <span class="font-medium">Thực Đơn</span>
                    </a>
                    <a href="lienhe&datban-new.html" class="mobile-nav-link flex items-center py-3 px-4 rounded-lg hover:bg-gray-100 transition duration-300" data-page="lienhe&datban">
                        <i class="fas fa-phone w-5 mr-3 text-gray-600"></i>
                        <span class="font-medium">Đặt Bàn & Liên Hệ</span>
                    </a>
                </div>

                <!-- Action Buttons Section -->
                <div class="space-y-2 mb-4">
                    <!-- Mobile Cart Button - Only visible on menu page -->
                    <div id="mobileCartContainer" class="hidden">
                        <button id="mobileCartBtn" class="w-full bg-primary hover:bg-red-700 text-white py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center shadow-md">
                            <i class="fas fa-shopping-cart mr-2"></i>
                            <span class="font-medium">Giỏ hàng (<span id="mobileCartCounter">0</span>)</span>
                        </button>
                    </div>

                    <!-- Mobile Invoice History Button - Only visible when logged in -->
                    <div id="mobileInvoiceHistoryContainer" class="hidden">
                        <button onclick="window.location.href='DanhSachHoaDon.html'" class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center border border-blue-200">
                            <i class="fas fa-receipt mr-2"></i>
                            <span class="font-medium">Lịch sử đơn hàng</span>
                        </button>
                    </div>
                </div>

                <!-- User Section -->
                <div class="border-t border-gray-200 pt-4">
                    <!-- User Greeting -->
                    <div id="mobileUserDisplay" class="hidden mb-3 px-4 py-2 bg-green-50 rounded-lg border border-green-200">
                        <div class="flex items-center">
                            <i class="fas fa-user-circle text-green-600 mr-2"></i>
                            <div>
                                <span class="text-sm text-gray-600">Xin chào,</span>
                                <div id="mobileUserName" class="font-semibold text-green-700"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="space-y-2">
                        <button id="mobileLoginBtn" class="w-full flex items-center justify-center py-3 px-4 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-lg transition duration-300 border border-gray-200">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            <span class="font-medium">Đăng Nhập</span>
                        </button>
                        <button id="mobileLogoutBtn" class="hidden w-full flex items-center justify-center py-3 px-4 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg transition duration-300 border border-red-200">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            <span class="font-medium">Đăng Xuất</span>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        
    </div>
</header>
