<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Navigation - Ẩm <PERSON>h<PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/mobile-responsive.css">
    <style>
        .primary { color: #dc2626; }
        .bg-primary { background-color: #dc2626; }
        .hover\:bg-red-700:hover { background-color: #b91c1c; }
        .text-primary { color: #dc2626; }
        .font-cursive { font-family: 'Dancing Script', cursive; }
        
        /* Test styles */
        .test-section {
            padding: 20px;
            margin: 20px 0;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>
    
    <!-- Main Content -->
    <main class="pt-20 pb-8">
        <div class="container mx-auto px-4">
            <div class="test-section">
                <h1 class="text-2xl font-bold mb-4">Test Mobile Navigation</h1>
                <p class="mb-4">Trang này để test navigation mobile mới. Hãy mở trên điện thoại hoặc thu nhỏ trình duyệt để xem.</p>
                
                <div class="mb-4">
                    <h3 class="font-semibold mb-2">Test Functions:</h3>
                    <button class="test-button" onclick="toggleMobileNav()">Toggle Mobile Nav</button>
                    <button class="test-button" onclick="simulateLogin()">Simulate Login</button>
                    <button class="test-button" onclick="simulateLogout()">Simulate Logout</button>
                    <button class="test-button" onclick="showCart()">Show Cart</button>
                    <button class="test-button" onclick="hideCart()">Hide Cart</button>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-semibold mb-2">Navigation Features:</h3>
                    <ul class="list-disc list-inside space-y-1 text-sm">
                        <li>✅ Improved visual hierarchy with icons</li>
                        <li>✅ Better spacing and grouping</li>
                        <li>✅ Enhanced hover effects</li>
                        <li>✅ Smooth animations</li>
                        <li>✅ User greeting section</li>
                        <li>✅ Action buttons (Cart, Invoice History)</li>
                        <li>✅ Responsive design</li>
                        <li>✅ No impact on PC version</li>
                    </ul>
                </div>
                
                <div class="mb-4">
                    <h3 class="font-semibold mb-2">Current Status:</h3>
                    <div id="status" class="text-sm bg-white p-3 rounded border">
                        <div>Mobile Nav: <span id="navStatus">Closed</span></div>
                        <div>User: <span id="userStatus">Not logged in</span></div>
                        <div>Cart: <span id="cartStatus">Hidden</span></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
    
    <script>
        // Test functions
        function toggleMobileNav() {
            const nav = document.getElementById('mobileNav');
            if (nav) {
                nav.classList.toggle('hidden');
                document.getElementById('navStatus').textContent = 
                    nav.classList.contains('hidden') ? 'Closed' : 'Open';
            }
        }
        
        function simulateLogin() {
            const userDisplay = document.getElementById('mobileUserDisplay');
            const userName = document.getElementById('mobileUserName');
            const loginBtn = document.getElementById('mobileLoginBtn');
            const logoutBtn = document.getElementById('mobileLogoutBtn');
            const invoiceContainer = document.getElementById('mobileInvoiceHistoryContainer');
            
            if (userDisplay && userName && loginBtn && logoutBtn) {
                userDisplay.classList.remove('hidden');
                userName.textContent = 'Nguyễn Văn A';
                loginBtn.classList.add('hidden');
                logoutBtn.classList.remove('hidden');
                if (invoiceContainer) invoiceContainer.classList.remove('hidden');
                document.getElementById('userStatus').textContent = 'Nguyễn Văn A';
            }
        }
        
        function simulateLogout() {
            const userDisplay = document.getElementById('mobileUserDisplay');
            const loginBtn = document.getElementById('mobileLoginBtn');
            const logoutBtn = document.getElementById('mobileLogoutBtn');
            const invoiceContainer = document.getElementById('mobileInvoiceHistoryContainer');
            
            if (userDisplay && loginBtn && logoutBtn) {
                userDisplay.classList.add('hidden');
                loginBtn.classList.remove('hidden');
                logoutBtn.classList.add('hidden');
                if (invoiceContainer) invoiceContainer.classList.add('hidden');
                document.getElementById('userStatus').textContent = 'Not logged in';
            }
        }
        
        function showCart() {
            const cartContainer = document.getElementById('mobileCartContainer');
            if (cartContainer) {
                cartContainer.classList.remove('hidden');
                document.getElementById('cartStatus').textContent = 'Visible';
            }
        }
        
        function hideCart() {
            const cartContainer = document.getElementById('mobileCartContainer');
            if (cartContainer) {
                cartContainer.classList.add('hidden');
                document.getElementById('cartStatus').textContent = 'Hidden';
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Load header component
            ComponentLoader.loadComponent('header', '#header-placeholder').then(() => {
                console.log('Header loaded for mobile nav test');
            });
        });
    </script>
</body>
</html>
