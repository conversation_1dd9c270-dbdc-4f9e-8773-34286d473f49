<!-- Footer Component -->
<footer class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.1\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'2\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E'); background-size: 60px 60px;"></div>
    </div>


    <div class="relative z-10 pt-16 pb-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <!-- Brand Section -->
                <div class="lg:col-span-1">
                    <div class="mb-6">
                        <h3 class="text-3xl font-cursive mb-2 text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-red-500">
                            Phương Nam
                        </h3>
                        <div class="w-16 h-1 bg-gradient-to-r from-yellow-400 to-red-500 rounded-full mb-4"></div>
                    </div>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        Nhà hàng Ẩm thực Phương Nam mang đến trải nghiệm ẩm thực Nam Bộ đích thực với không gian sang trọng và ấm cúng.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center hover:from-blue-500 hover:to-blue-600 transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
                            <i class="fab fa-facebook-f text-sm"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-pink-600 to-purple-600 rounded-full flex items-center justify-center hover:from-pink-500 hover:to-purple-500 transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
                            <i class="fab fa-instagram text-sm"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-red-600 to-red-700 rounded-full flex items-center justify-center hover:from-red-500 hover:to-red-600 transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
                            <i class="fab fa-youtube text-sm"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gradient-to-r from-gray-800 to-black rounded-full flex items-center justify-center hover:from-gray-700 hover:to-gray-800 transition-all duration-300 transform hover:scale-110 hover:shadow-lg">
                            <i class="fab fa-tiktok text-sm"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-xl font-bold mb-6 text-white">
                        <i class="fas fa-link mr-2 text-yellow-400"></i>
                        Liên Kết Nhanh
                    </h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="Index.html" class="text-gray-300 hover:text-yellow-400 transition-all duration-300 flex items-center group">
                                <i class="fas fa-home mr-3 text-yellow-400 opacity-70 group-hover:opacity-100 transition-opacity"></i>
                                <span class="group-hover:translate-x-1 transition-transform duration-300">Trang Chủ</span>
                            </a>
                        </li>
                        <li>
                            <a href="gioithieu.html" class="text-gray-300 hover:text-yellow-400 transition-all duration-300 flex items-center group">
                                <i class="fas fa-info-circle mr-3 text-yellow-400 opacity-70 group-hover:opacity-100 transition-opacity"></i>
                                <span class="group-hover:translate-x-1 transition-transform duration-300">Giới Thiệu</span>
                            </a>
                        </li>
                        <li>
                            <a href="Menu-new.html" class="text-gray-300 hover:text-yellow-400 transition-all duration-300 flex items-center group">
                                <i class="fas fa-utensils mr-3 text-yellow-400 opacity-70 group-hover:opacity-100 transition-opacity"></i>
                                <span class="group-hover:translate-x-1 transition-transform duration-300">Thực Đơn</span>
                            </a>
                        </li>
                        <li>
                            <a href="lienhe&datban.html" class="text-gray-300 hover:text-yellow-400 transition-all duration-300 flex items-center group">
                                <i class="fas fa-calendar-alt mr-3 text-yellow-400 opacity-70 group-hover:opacity-100 transition-opacity"></i>
                                <span class="group-hover:translate-x-1 transition-transform duration-300">Đặt Bàn</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-gray-300 hover:text-yellow-400 transition-all duration-300 flex items-center group">
                                <i class="fas fa-blog mr-3 text-yellow-400 opacity-70 group-hover:opacity-100 transition-opacity"></i>
                                <span class="group-hover:translate-x-1 transition-transform duration-300">Blog</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-gray-300 hover:text-yellow-400 transition-all duration-300 flex items-center group">
                                <i class="fas fa-briefcase mr-3 text-yellow-400 opacity-70 group-hover:opacity-100 transition-opacity"></i>
                                <span class="group-hover:translate-x-1 transition-transform duration-300">Tuyển Dụng</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-xl font-bold mb-6 text-white">
                        <i class="fas fa-phone mr-2 text-green-400"></i>
                        Thông Tin Liên Hệ
                    </h4>
                    <ul class="space-y-4">
                        <li class="flex items-start group">
                            <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-map-marker-alt text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-gray-300 leading-relaxed">
                                    Đ. Trương Văn Kĩnh/258 Phú Hòa,<br>
                                    Long Đức, Trà Vinh, 87000, Vietnam
                                </p>
                            </div>
                        </li>
                        <li class="flex items-start group">
                            <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-phone-alt text-white text-sm"></i>
                            </div>
                            <div>
                                <a href="tel:0374514494" class="text-gray-300 hover:text-green-400 transition-colors duration-300">
                                    0374514494
                                </a>
                            </div>
                        </li>
                        <li class="flex items-start group">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4 flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-envelope text-white text-sm"></i>
                            </div>
                            <div>
                                <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-blue-400 transition-colors duration-300">
                                    <EMAIL>
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Newsletter -->
                <div>
                    <h4 class="text-xl font-bold mb-6 text-white">
                        <i class="fas fa-envelope-open mr-2 text-purple-400"></i>
                        Đăng Ký Nhận Tin
                    </h4>
                    <p class="text-gray-300 mb-6 leading-relaxed">
                        Đăng ký để nhận thông tin về ưu đãi đặc biệt và sự kiện mới nhất.
                    </p>
                    <form class="space-y-4">
                        <div class="relative">
                            <input
                                type="email"
                                placeholder="Email của bạn"
                                class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent transition-all duration-300"
                            >
                        </div>
                        <button
                            type="submit"
                            class="w-full bg-gradient-to-r from-yellow-500 to-red-500 hover:from-yellow-400 hover:to-red-400 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center justify-center"
                        >
                            <i class="fas fa-paper-plane mr-2"></i>
                            Đăng Ký Ngay
                        </button>
                    </form>

                    <!-- Operating Hours -->
                    <div class="mt-8 p-4 bg-gray-800 rounded-lg border border-gray-700">
                        <h5 class="text-lg font-semibold mb-3 text-yellow-400">
                            <i class="fas fa-clock mr-2"></i>
                            Giờ Hoạt Động
                        </h5>
                        <div class="space-y-2 text-sm text-gray-300">
                            <div class="flex justify-between">
                                <span>Thứ 2 - Thứ 6:</span>
                                <span class="text-green-400">10:00 - 22:00</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Thứ 7 - Chủ Nhật:</span>
                                <span class="text-green-400">09:00 - 23:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Section -->
            <div class="border-t border-gray-700 pt-8 mt-12">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-center md:text-left mb-4 md:mb-0">
                        <p class="text-gray-400">
                            &copy; 2023 <span class="text-yellow-400 font-semibold">Nhà Hàng Ẩm Thực Phương Nam</span>. All rights reserved.
                        </p>
                    </div>
                    <div class="flex space-x-6 text-sm text-gray-400">
                        <a href="#" class="hover:text-yellow-400 transition-colors duration-300">Chính Sách Bảo Mật</a>
                        <a href="#" class="hover:text-yellow-400 transition-colors duration-300">Điều Khoản Sử Dụng</a>
                        <a href="#" class="hover:text-yellow-400 transition-colors duration-300">Sitemap</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
