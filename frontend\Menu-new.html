<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thực <PERSON> - <PERSON><PERSON> Ẩm <PERSON>hự<PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Dancing+Script:wght@600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/main.css" rel="stylesheet">
    <link href="css/menu-search.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    
    <!-- External JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    
    <!-- Page specific styles -->
    <style>
        /* Menu specific styles */
        .menu-filter-container {
            background: linear-gradient(135deg, #f6ad55 0%, #e53e3e 100%);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .menu-category-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .menu-category-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .menu-category-btn.active {
            background-color: #fbbf24 !important;
            color: white !important;
            border-color: #f59e0b;
            transform: scale(1.05);
        }
        
        .menu-item {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
            background: #ffffff;
            filter: contrast(1.05) brightness(1.02);
        }

        .menu-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border-color: #f59e0b;
            filter: contrast(1.1) brightness(1.05);
        }
        
        .search-container {
            position: relative;
            max-width: 600px;
            margin: 0 auto 2rem;
        }
        
        .search-results-info {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
        }
        
        .price-tag {
            background: linear-gradient(45deg, #e53e3e, #dc2626);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .add-to-cart-btn {
            background: linear-gradient(45deg, #e53e3e, #dc2626);
            transition: all 0.3s ease;
        }
        
        .add-to-cart-btn:hover {
            background: linear-gradient(45deg, #dc2626, #b91c1c);
            transform: scale(1.02);
        }

        /* Button animations */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .add-to-cart-btn:disabled {
            cursor: not-allowed;
            opacity: 0.9;
        }
        
        /* Loading animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #e53e3e;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Floating Checkout Button */
        #floatingCheckoutBtn {
            animation: bounceIn 0.6s ease-out;
        }

        #floatingCheckoutBtn button {
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
            backdrop-filter: blur(10px);
        }

        #floatingCheckoutBtn button:hover {
            box-shadow: 0 12px 35px rgba(220, 38, 38, 0.6);
            transform: scale(1.05) translateY(-2px);
        }

        @keyframes bounceIn {
            0% {
                transform: scale(0.3) translateY(100px);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        #floatingCheckoutBtn.pulse {
            animation: pulse 2s infinite;
        }

        /* Enhanced Quantity Controls Styling - High Contrast & Clear */
        .quantity-cart-container {
            transition: all 0.2s ease;
            filter: contrast(1.1) brightness(1.05);
        }

        .quantity-btn {
            transition: all 0.15s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
            font-weight: 900;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .quantity-btn:hover {
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.35);
            filter: brightness(1.1) saturate(1.2);
        }

        .quantity-btn:active {
            transform: scale(0.92);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .quantity-display {
            transition: all 0.15s ease;
            user-select: none;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            border: 3px solid #9ca3af !important;
            background: linear-gradient(145deg, #f3f4f6, #e5e7eb) !important;
        }

        /* Enhanced button styling */
        .add-to-cart-btn {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-weight: 800;
            letter-spacing: 0.5px;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .add-to-cart-btn:hover {
            filter: brightness(1.1) saturate(1.1);
        }

        /* Enhanced shake animation */
        @keyframes shake {
            0%, 100% {
                transform: translateX(0) scale(1);
                filter: brightness(1);
            }
            10%, 30%, 50%, 70%, 90% {
                transform: translateX(-4px) scale(1.05);
                filter: brightness(1.2);
            }
            20%, 40%, 60%, 80% {
                transform: translateX(4px) scale(1.05);
                filter: brightness(1.2);
            }
        }

        /* Improved hover effects for menu items */
        .menu-item:hover .quantity-cart-container {
            transform: translateY(-2px);
        }

        /* Warning notification styling */
        .notification-warning {
            background: linear-gradient(45deg, #f59e0b, #d97706);
        }

        /* Enhanced text clarity */
        .menu-item h3 {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            color: #1f2937;
            font-weight: 800;
        }

        .menu-item p {
            color: #374151;
            font-weight: 500;
            line-height: 1.6;
        }

        .price-tag {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            font-weight: 900;
            font-size: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Enhanced image clarity */
        .menu-item img {
            filter: contrast(1.1) saturate(1.1) brightness(1.02);
            transition: all 0.3s ease;
        }

        .menu-item:hover img {
            filter: contrast(1.15) saturate(1.15) brightness(1.05);
        }

        /* Image Lightbox Styles */
        #imageLightbox {
            backdrop-filter: blur(10px);
            animation: fadeIn 0.3s ease-out;
        }

        #imageLightbox.hidden {
            animation: fadeOut 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        #lightboxImage {
            transition: transform 0.3s ease;
            cursor: zoom-out;
        }

        #lightboxImage:hover {
            transform: scale(1.02);
        }

        .menu-item img {
            cursor: zoom-in;
            transition: all 0.3s ease;
            position: relative;
        }

        .menu-item img:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
        }

        /* Add zoom icon overlay on hover */
        .menu-item .h-64:hover::after {
            content: '\f00e';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem;
            border-radius: 50%;
            font-size: 1.5rem;
            opacity: 0;
            animation: zoomIconFadeIn 0.3s ease forwards;
            pointer-events: none;
            z-index: 10;
        }

        @keyframes zoomIconFadeIn {
            from { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        /* 4K Image Quality Enhancements */
        .progressive-image {
            transition: filter 0.5s ease, opacity 0.3s ease;
        }

        .quality-indicator {
            backdrop-filter: blur(4px);
            animation: qualityBadgeSlideIn 0.5s ease-out;
        }

        .lightbox-quality-badge {
            animation: qualityBadgeSlideIn 0.5s ease-out;
            backdrop-filter: blur(8px);
        }

        @keyframes qualityBadgeSlideIn {
            from {
                opacity: 0;
                transform: translateX(20px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        /* Image loading states */
        .image-loading-indicator {
            backdrop-filter: blur(4px);
        }

        /* Enhanced image quality display */
        .menu-item img[data-loaded="true"] {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        /* 4K quality enhancement effects */
        .ultra-quality {
            filter: contrast(1.05) saturate(1.1) brightness(1.02);
        }

        /* Lightbox image enhancements */
        #lightboxImage {
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            max-width: 100%;
            max-height: 85vh;
        }

        /* Quality badge animations */
        .quality-indicator, .lightbox-quality-badge {
            user-select: none;
            pointer-events: none;
        }

        /* Progressive loading blur effect */
        .progressive-image[data-loading="true"] {
            filter: blur(3px);
            opacity: 0.8;
        }

        /* Detailed Info Modal Styles */
        #detailedInfoModal {
            backdrop-filter: blur(8px);
            animation: modalFadeIn 0.3s ease-out;
        }

        #detailedInfoModal.hidden {
            animation: modalFadeOut 0.3s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes modalFadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.95);
            }
        }

        /* Detailed info sections */
        #detailedInfoModal h3 {
            border-left: 4px solid;
            padding-left: 1rem;
        }

        #detailedInfoModal h3:nth-of-type(1) { border-color: #f97316; }
        #detailedInfoModal h3:nth-of-type(2) { border-color: #10b981; }
        #detailedInfoModal h3:nth-of-type(3) { border-color: #ef4444; }
        #detailedInfoModal h3:nth-of-type(4) { border-color: #ec4899; }
        #detailedInfoModal h3:nth-of-type(5) { border-color: #3b82f6; }
        #detailedInfoModal h3:nth-of-type(6) { border-color: #16a34a; }
        #detailedInfoModal h3:nth-of-type(7) { border-color: #8b5cf6; }

        /* Ingredients grid */
        #detailIngredients li {
            background: #f8fafc;
            padding: 0.5rem;
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
        }

        /* Scroll styling */
        #detailedInfoModal::-webkit-scrollbar {
            width: 8px;
        }

        #detailedInfoModal::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        #detailedInfoModal::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        #detailedInfoModal::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* View Details Button */
        .view-details-btn {
            position: relative;
            overflow: hidden;
        }

        .view-details-btn:hover {
            transform: translateX(2px);
        }

        .view-details-btn::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background: currentColor;
            transition: width 0.3s ease;
        }

        .view-details-btn:hover::before {
            width: 100%;
        }

        .view-details-btn i.fa-chevron-right {
            transition: transform 0.2s ease;
        }

        .view-details-btn:hover i.fa-chevron-right {
            transform: translateX(2px);
        }

        /* Navigation arrows */
        #prevImage, #nextImage {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        #prevImage:hover, #nextImage:hover {
            background: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }

        /* Responsive quantity controls */
        @media (max-width: 640px) {
            .quantity-btn {
                width: 2.5rem;
                height: 2.5rem;
                font-size: 0.875rem;
            }

            .quantity-display {
                min-width: 3rem;
                padding: 0.5rem 0.75rem;
                font-size: 1.125rem;
                font-weight: 900;
            }

            .add-to-cart-btn {
                padding: 1rem 1.5rem;
                font-size: 1rem;
                font-weight: 800;
            }

            /* Mobile lightbox adjustments */
            #imageLightbox .relative {
                margin: 1rem;
            }

            #lightboxImage {
                max-height: 70vh;
            }

            #closeLightbox {
                top: -3rem;
                right: 1rem;
                font-size: 2rem;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <!-- Ad Banner Placeholder -->
    <div id="ad-banner-placeholder"></div>

    <!-- Main Content -->
    <main>
        <!-- Menu Section Content -->
        <section class="py-12" id="menuSection">
            <div class="container mx-auto px-4">


                <!-- Page Title -->
                <div class="text-center mb-12 fade-in" id="menuTitle">
                    <h2 class="text-4xl font-bold mb-4">Thực Đơn</h2>
                    <p class="text-gray-600 text-lg">Khám phá hương vị Nam Bộ đích thực</p>


                </div>

                <!-- Menu Filter Container -->
                <div class="menu-filter-container slide-in-left">
                    <!-- Search Bar -->
                    <div class="search-container mb-6">
                        <div class="relative">
                            <input 
                                type="text" 
                                id="searchInput"
                                class="w-full px-6 py-4 border-2 border-white rounded-full focus:outline-none focus:ring-4 focus:ring-yellow-300 text-lg shadow-lg"
                                placeholder="🔍 Tìm kiếm món ăn yêu thích..."
                            >
                            <button 
                                id="searchButton"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-primary text-white px-4 py-2 rounded-full hover:bg-red-700 transition duration-300"
                                type="button"
                            >
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Menu Categories -->
                    <div class="flex flex-wrap justify-center gap-3" id="menuFilter">
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="all">
                            <i class="fas fa-utensils mr-2"></i>Tất Cả
                        </button>
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="appetizers">
                            <i class="fas fa-leaf mr-2"></i>Khai Vị
                        </button>
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="maindishes">
                            <i class="fas fa-drumstick-bite mr-2"></i>Món Chính
                        </button>
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="rice">
                            <i class="fas fa-bowl-rice mr-2"></i>Cơm & Bún
                        </button>
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="hotpot">
                            <i class="fas fa-fire mr-2"></i>Canh & Lẩu
                        </button>
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="desserts">
                            <i class="fas fa-ice-cream mr-2"></i>Tráng Miệng
                        </button>
                        <button class="menu-category-btn bg-white text-gray-800 px-6 py-3 rounded-full font-semibold shadow-md" data-category="drinks">
                            <i class="fas fa-glass-water mr-2"></i>Đồ Uống
                        </button>
                    </div>
                </div>

                <!-- Search Results Info -->
                <div id="searchResultsInfo" class="hidden mb-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div id="searchResultsText" class="flex items-center justify-between">
                            <!-- Search results text will be inserted here -->
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="loadingState" class="text-center py-8 hidden">
                    <div class="loading-spinner mx-auto mb-4"></div>
                    <p class="text-gray-600">Đang tải món ăn...</p>
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="empty-state hidden">
                    <i class="fas fa-search"></i>
                    <h3 class="text-xl font-bold mb-2">Không tìm thấy món ăn</h3>
                    <p>Thử tìm kiếm với từ khóa khác hoặc chọn danh mục khác</p>
                </div>

                <!-- Menu Items Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="menuItemsContainer">
                    <!-- Menu items will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <!-- Chatbot Placeholder -->
    <div id="chatbot-placeholder"></div>

    <!-- Login Modal Placeholder -->
    <div id="login-modal-placeholder"></div>

    <!-- Customer Info Modal Placeholder -->
    <div id="customer-info-placeholder"></div>

    <!-- Cart Modal Placeholder -->
    <div id="cart-modal-placeholder"></div>

    <!-- Floating Contact Placeholder -->
    <div id="floating-contact-placeholder"></div>

    <!-- Image Lightbox Modal -->
    <div id="imageLightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <!-- Close Button -->
            <button id="closeLightbox" class="absolute -top-12 right-0 text-white text-4xl hover:text-gray-300 transition-colors z-10">
                <i class="fas fa-times"></i>
            </button>

            <!-- Image Container -->
            <div class="relative bg-white rounded-lg overflow-hidden shadow-2xl">
                <div id="imageZoomContainer" class="relative overflow-hidden cursor-zoom-in">
                    <img id="lightboxImage" src="" alt="" class="max-w-full max-h-[80vh] object-contain transition-transform duration-300">
                </div>

                <!-- Image Info Overlay -->
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent p-6">
                    <h3 id="lightboxTitle" class="text-white text-2xl font-bold mb-2"></h3>
                    <p id="lightboxDescription" class="text-gray-200 text-sm leading-relaxed mb-3"></p>

                    <!-- Detailed Info Button -->
                    <button id="showDetailedInfo" class="text-blue-300 hover:text-blue-200 text-sm mb-3 flex items-center">
                        <i class="fas fa-info-circle mr-1"></i>Xem thông tin chi tiết
                    </button>

                    <div class="flex items-center justify-between">
                        <span id="lightboxPrice" class="text-yellow-400 text-xl font-bold"></span>
                        <button id="lightboxAddToCart" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-cart-plus mr-2"></i>Thêm vào giỏ
                        </button>
                    </div>
                </div>
            </div>

            <!-- Navigation Arrows -->
            <button id="prevImage" class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-3xl hover:text-gray-300 transition-colors">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button id="nextImage" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-3xl hover:text-gray-300 transition-colors">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <!-- Detailed Info Modal -->
    <div id="detailedInfoModal" class="fixed inset-0 bg-black bg-opacity-75 z-60 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-4xl max-h-[90vh] overflow-y-auto shadow-2xl">
            <!-- Header -->
            <div class="sticky top-0 bg-white border-b border-gray-200 p-6 flex items-center justify-between">
                <h2 id="detailModalTitle" class="text-2xl font-bold text-gray-800"></h2>
                <button id="closeDetailModal" class="text-gray-500 hover:text-gray-700 text-2xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Content -->
            <div class="p-6">
                <!-- Main Description -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-utensils mr-2 text-orange-500"></i>Mô tả chi tiết
                    </h3>
                    <p id="detailDescription" class="text-gray-600 leading-relaxed"></p>
                </div>

                <!-- Ingredients -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-list-ul mr-2 text-green-500"></i>Nguyên liệu
                    </h3>
                    <ul id="detailIngredients" class="grid grid-cols-1 md:grid-cols-2 gap-2"></ul>
                </div>

                <!-- Cooking Method -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-fire mr-2 text-red-500"></i>Cách chế biến
                    </h3>
                    <p id="detailCookingMethod" class="text-gray-600 leading-relaxed"></p>
                </div>

                <!-- Taste Profile -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-heart mr-2 text-pink-500"></i>Hương vị đặc trưng
                    </h3>
                    <p id="detailTasteProfile" class="text-gray-600 leading-relaxed"></p>
                </div>

                <!-- Serving Suggestion -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-concierge-bell mr-2 text-blue-500"></i>Cách thưởng thức
                    </h3>
                    <p id="detailServingSuggestion" class="text-gray-600 leading-relaxed"></p>
                </div>

                <!-- Nutritional Info -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-leaf mr-2 text-green-600"></i>Giá trị dinh dưỡng
                    </h3>
                    <p id="detailNutritionalInfo" class="text-gray-600 leading-relaxed"></p>
                </div>

                <!-- Origin -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-map-marker-alt mr-2 text-purple-500"></i>Nguồn gốc
                    </h3>
                    <p id="detailOrigin" class="text-gray-600 leading-relaxed"></p>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="text-2xl font-bold text-orange-600" id="detailPrice"></div>
                    <div class="flex space-x-3">
                        <button id="detailAddToCart" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            <i class="fas fa-cart-plus mr-2"></i>Thêm vào giỏ hàng
                        </button>
                        <button id="closeDetailModalBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                            Đóng
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/auth.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>
    <script src="js/chatbot.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/user-cart-integration.js"></script>
    <script src="js/menu-api.js"></script>
    <script src="js/menu.js"></script>

    <!-- Initialize menu -->
    <script>
        // Debug cart info (hidden function, accessible via console)
        function debugCartInfo() {
            if (window.cartManager) {
                const info = window.cartManager.debugCartInfo();
                console.log('🔍 Cart Debug Info:', info);
                return info;
            }
        }

        // Test login function (for debugging)
        function testLogin() {
            const testUser = {
                id: 'test123',
                email: '<EMAIL>',
                full_name: 'Test User',
                phone: '0123456789'
            };
            localStorage.setItem('user', JSON.stringify(testUser));
            console.log('✅ Test user logged in:', testUser);

            // Trigger cart manager to reload customer info
            if (window.cartManager) {
                window.cartManager.handleUserChange();
            }
        }

        // Test logout function (for debugging)
        function testLogout() {
            localStorage.removeItem('user');
            localStorage.removeItem('userData');
            localStorage.removeItem('token');
            console.log('✅ Test user logged out');

            // Trigger cart manager to reload customer info
            if (window.cartManager) {
                window.cartManager.handleUserChange();
            }
        }

        // Initialize menu when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Menu-new.html loaded successfully!');

            // Initialize cart manager first
            setTimeout(() => {
                if (!window.cartManager) {
                    window.cartManager = new CartManager();
                    console.log('🛒 CartManager initialized');
                }

                // Then initialize menu manager
                window.menuManager = new MenuManager();
                console.log('📋 MenuManager initialized');

                // Cart system is ready and working in background

                // Test cart functionality
                setTimeout(() => {
                    const cartBtn = document.getElementById('cartBtn');
                    const cartModal = document.getElementById('cartModal');
                    console.log('🔍 Cart button found:', !!cartBtn);
                    console.log('🔍 Cart modal found:', !!cartModal);

                    if (cartBtn) {
                        console.log('🔍 Cart button classes:', cartBtn.className);
                        console.log('🔍 Cart button visible:', !cartBtn.classList.contains('hidden'));
                    }
                }, 1000);

            }, 500);

            // Cart system continues to work in background
            // Each user maintains their own separate cart automatically
        });

    </script>
</body>
</html>
