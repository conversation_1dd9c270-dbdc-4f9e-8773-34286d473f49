<!-- Floating Contact Buttons Component -->
<div id="floatingContact" class="fixed left-4 bottom-20 z-50 flex flex-col space-y-8">
    <!-- Zalo Button -->
    <div class="floating-btn zalo-btn group">
        <a href="https://zalo.me/0374514494" target="_blank" class="relative block">
            <!-- Main Button -->
            <div class="w-16 h-16 bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-2xl transform transition-all duration-500 group-hover:scale-125 group-hover:shadow-blue-500/50 group-hover:shadow-2xl border-2 border-blue-300/30">
                <!-- Zalo Icon -->
                <div class="relative flex items-center justify-center">
                    <!-- Zalo Text Logo -->
                    <div class="text-white font-bold text-lg tracking-wide drop-shadow-lg">
                        Zalo
                    </div>
                    <!-- Inner glow -->
                    <div class="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
            </div>

            <!-- Enhanced Ripple Effects -->
            <div class="absolute inset-0 rounded-full bg-blue-400 opacity-40 animate-ping"></div>
            <div class="absolute inset-0 rounded-full bg-blue-300 opacity-25 animate-ping animation-delay-700"></div>
            <div class="absolute inset-0 rounded-full bg-blue-500 opacity-15 animate-ping animation-delay-1400"></div>

            <!-- Enhanced Tooltip -->
            <div class="absolute left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-800 to-gray-900 text-white px-4 py-3 rounded-xl text-sm font-medium whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none shadow-xl border border-gray-700">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>Chat Zalo</span>
                </div>
                <div class="absolute right-full top-1/2 transform -translate-y-1/2 border-8 border-transparent border-r-gray-800"></div>
            </div>
        </a>
    </div>

    <!-- Phone Button -->
    <div class="floating-btn phone-btn group">
        <a href="tel:0374514494" class="relative block">
            <!-- Main Button -->
            <div class="w-16 h-16 bg-gradient-to-br from-green-400 via-green-500 to-green-600 rounded-full flex items-center justify-center shadow-2xl transform transition-all duration-500 group-hover:scale-125 group-hover:shadow-green-500/50 group-hover:shadow-2xl border-2 border-green-300/30">
                <!-- Phone Icon -->
                <div class="relative">
                    <svg class="w-9 h-9 text-white drop-shadow-lg transform group-hover:rotate-12 group-hover:scale-110 transition-all duration-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                    </svg>
                    <!-- Inner glow -->
                    <div class="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
            </div>

            <!-- Enhanced Ripple Effects -->
            <div class="absolute inset-0 rounded-full bg-green-400 opacity-40 animate-ping"></div>
            <div class="absolute inset-0 rounded-full bg-green-300 opacity-25 animate-ping animation-delay-600"></div>
            <div class="absolute inset-0 rounded-full bg-green-500 opacity-15 animate-ping animation-delay-1200"></div>

            <!-- Enhanced Tooltip -->
            <div class="absolute left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-gray-800 to-gray-900 text-white px-4 py-3 rounded-xl text-sm font-medium whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none shadow-xl border border-gray-700">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>Gọi: 037.451.4494</span>
                </div>
                <div class="absolute right-full top-1/2 transform -translate-y-1/2 border-8 border-transparent border-r-gray-800"></div>
            </div>
        </a>
    </div>

    <!-- WhatsApp Button (Optional) -->
    <div class="floating-btn whatsapp-btn group hidden">
        <a href="https://wa.me/84374514494" target="_blank" class="relative block">
            <!-- Main Button -->
            <div class="w-14 h-14 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl">
                <!-- WhatsApp Icon -->
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
            </div>
            
            <!-- Ripple Effect -->
            <div class="absolute inset-0 rounded-full bg-green-400 opacity-30 animate-ping"></div>
            
            <!-- Tooltip -->
            <div class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                Chat WhatsApp
                <div class="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-800"></div>
            </div>
        </a>
    </div>
</div>

<style>
/* Enhanced Floating Contact Buttons Styles */
.floating-btn {
    position: relative;
    animation: float 4s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.15));
}

.floating-btn:nth-child(1) {
    animation-delay: -0.5s;
}

.floating-btn:nth-child(2) {
    animation-delay: -2s;
}

.floating-btn:nth-child(3) {
    animation-delay: -3.5s;
}

/* Enhanced Float Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-8px) rotate(1deg);
    }
    50% {
        transform: translateY(-15px) rotate(0deg);
    }
    75% {
        transform: translateY(-8px) rotate(-1deg);
    }
}

/* Enhanced Ping Animation Delays */
.animation-delay-600 {
    animation-delay: 0.6s;
}

.animation-delay-700 {
    animation-delay: 0.7s;
}

.animation-delay-1200 {
    animation-delay: 1.2s;
}

.animation-delay-1400 {
    animation-delay: 1.4s;
}

/* Pulse Animation for Active State */
.floating-btn:active .w-14 {
    animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

/* Enhanced Glow Effects */
.zalo-btn:hover .w-16 {
    box-shadow:
        0 0 30px rgba(59, 130, 246, 0.8),
        0 0 60px rgba(59, 130, 246, 0.4),
        0 0 90px rgba(59, 130, 246, 0.2);
    transform: scale(1.25) translateY(-5px);
}

.phone-btn:hover .w-16 {
    box-shadow:
        0 0 30px rgba(239, 68, 68, 0.8),
        0 0 60px rgba(239, 68, 68, 0.4),
        0 0 90px rgba(239, 68, 68, 0.2);
    transform: scale(1.25) translateY(-5px);
}

.whatsapp-btn:hover .w-16 {
    box-shadow:
        0 0 30px rgba(34, 197, 94, 0.8),
        0 0 60px rgba(34, 197, 94, 0.4),
        0 0 90px rgba(34, 197, 94, 0.2);
    transform: scale(1.25) translateY(-5px);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    #floatingContact {
        left: 12px;
        bottom: 80px;
        space-y: 6;
    }

    .floating-btn .w-16 {
        width: 3.5rem;
        height: 3.5rem;
    }

    .floating-btn svg {
        width: 1.75rem;
        height: 1.75rem;
    }

    .floating-btn .absolute.left-20 {
        left: 4rem;
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 640px) {
    #floatingContact {
        space-y: 5;
    }

    .floating-btn .w-16 {
        width: 3rem;
        height: 3rem;
    }

    .floating-btn svg {
        width: 1.5rem;
        height: 1.5rem;
    }
}

/* Hide tooltips on very small screens */
@media (max-width: 480px) {
    .floating-btn .absolute.left-20 {
        display: none;
    }

    #floatingContact {
        space-y: 4;
    }
}

/* Enhanced entrance animation from left */
.floating-btn {
    opacity: 0;
    transform: translateX(-120px) scale(0.5);
    animation: slideInFloat 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.floating-btn:nth-child(1) {
    animation-delay: 0.3s;
}

.floating-btn:nth-child(2) {
    animation-delay: 0.8s;
}

.floating-btn:nth-child(3) {
    animation-delay: 1.3s;
}

@keyframes slideInFloat {
    0% {
        opacity: 0;
        transform: translateX(-120px) scale(0.5) rotate(-180deg);
    }
    60% {
        opacity: 0.8;
        transform: translateX(10px) scale(1.1) rotate(10deg);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1) rotate(0deg);
    }
}

/* After slide in, start floating */
.floating-btn {
    animation: slideInFloat 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards,
               float 4s ease-in-out infinite 1.5s;
}

/* Notification Badge (Optional) */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #ff4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Click ripple effect */
.floating-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.floating-btn:active::before {
    width: 60px;
    height: 60px;
}

/* Accessibility */
.floating-btn:focus-within {
    outline: 2px solid #4F46E5;
    outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .floating-btn .absolute.right-16 {
        background: #1F2937;
        color: #F9FAFB;
    }
    
    .floating-btn .absolute.right-16::after {
        border-left-color: #1F2937;
    }
}
</style>

<script>
// Floating Contact Buttons JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const floatingContact = document.getElementById('floatingContact');
    
    // Show/hide based on scroll position
    let lastScrollTop = 0;
    let isVisible = true;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Hide when scrolling down fast, show when scrolling up
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            if (isVisible) {
                floatingContact.style.transform = 'translateX(-100px)';
                floatingContact.style.opacity = '0';
                isVisible = false;
            }
        } else {
            // Scrolling up
            if (!isVisible) {
                floatingContact.style.transform = 'translateX(0)';
                floatingContact.style.opacity = '1';
                isVisible = true;
            }
        }

        lastScrollTop = scrollTop;
    });
    
    // Add click tracking for analytics
    const buttons = floatingContact.querySelectorAll('a');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const type = this.href.includes('zalo') ? 'zalo' : 
                        this.href.includes('tel') ? 'phone' : 
                        this.href.includes('wa.me') ? 'whatsapp' : 'unknown';
            
            // Track click event (you can integrate with Google Analytics here)
            console.log(`Floating contact clicked: ${type}`);
            
            // Add visual feedback
            const btn = this.querySelector('.w-14');
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = '';
            }, 150);
        });
    });
    
    // Add notification badge for new messages (optional)
    function addNotificationBadge(buttonType, count) {
        const button = floatingContact.querySelector(`.${buttonType}-btn`);
        if (button && count > 0) {
            const badge = document.createElement('div');
            badge.className = 'notification-badge';
            badge.textContent = count > 9 ? '9+' : count;
            button.appendChild(badge);
        }
    }
    
    // Example: Add notification badge
    // addNotificationBadge('zalo', 3);
    
    // Remove notification badge when clicked
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            const badge = this.querySelector('.notification-badge');
            if (badge) {
                badge.remove();
            }
        });
    });
    
    // Keyboard accessibility
    buttons.forEach(button => {
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
});

// Global function to show/hide floating contact
window.toggleFloatingContact = function(show = true) {
    const floatingContact = document.getElementById('floatingContact');
    if (show) {
        floatingContact.style.display = 'flex';
    } else {
        floatingContact.style.display = 'none';
    }
};

// Global function to add notification badge
window.addContactNotification = function(type, count) {
    const floatingContact = document.getElementById('floatingContact');
    const button = floatingContact.querySelector(`.${type}-btn`);
    if (button) {
        // Remove existing badge
        const existingBadge = button.querySelector('.notification-badge');
        if (existingBadge) {
            existingBadge.remove();
        }
        
        // Add new badge if count > 0
        if (count > 0) {
            const badge = document.createElement('div');
            badge.className = 'notification-badge';
            badge.textContent = count > 9 ? '9+' : count;
            button.appendChild(badge);
        }
    }
};
</script>
