<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Persistent Dialog - <PERSON><PERSON><PERSON> Ẩm <PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .btn-primary { background: #3b82f6; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        
        .instruction-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .feature-list {
            background: #f0fdf4;
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .logs {
            background: #1f2937;
            color: #10b981;
            padding: 1.5rem;
            border-radius: 12px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }
    </style>
</head>
<body class="p-6">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center text-white mb-8">
            <h1 class="text-4xl font-bold mb-3">
                🔒 Test Persistent Session Dialog
            </h1>
            <p class="text-xl opacity-90">Kiểm tra thông báo không tự biến mất khi token hết hạn</p>
        </div>

        <!-- Quick Test Section -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-6 text-center text-gray-800">
                🚀 Quick Test Controls
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button onclick="testLogin()" class="btn btn-primary">
                    🔐 Login Test
                </button>
                <button onclick="showPersistentDialog()" class="btn btn-danger">
                    🚨 Show Persistent Dialog
                </button>
                <button onclick="testAutoLogout()" class="btn btn-warning">
                    ⏰ Test Auto Logout (10s)
                </button>
                <button onclick="clearAll()" class="btn btn-secondary">
                    🗑️ Clear All
                </button>
            </div>
        </div>

        <!-- Instructions -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">
                📋 Hướng Dẫn Test Persistent Dialog
            </h2>
            
            <div class="instruction-box">
                <h3 class="text-lg font-bold text-yellow-800 mb-3">
                    ⚠️ Tính Năng Persistent Dialog:
                </h3>
                <ul class="space-y-2 text-yellow-700">
                    <li>• <strong>KHÔNG tự biến mất:</strong> Dialog sẽ ở lại màn hình cho đến khi user xác nhận</li>
                    <li>• <strong>Có nút X:</strong> Ở góc phải header để đóng dialog</li>
                    <li>• <strong>Không đóng được bằng ESC:</strong> Bấm ESC sẽ có hiệu ứng shake</li>
                    <li>• <strong>Không đóng được bằng click outside:</strong> Click ngoài dialog sẽ có hiệu ứng shake</li>
                    <li>• <strong>User PHẢI xác nhận:</strong> Bắt buộc click nút để đóng</li>
                </ul>
            </div>

            <div class="feature-list">
                <h3 class="text-lg font-bold text-green-800 mb-3">
                    ✅ Các Tính Năng Đã Implement:
                </h3>
                <ul class="space-y-2 text-green-700">
                    <li>• <strong>Nút X:</strong> Ở góc phải header (màu đỏ, hover effect)</li>
                    <li>• <strong>Nút "Đã Hiểu":</strong> Xác nhận và đóng dialog</li>
                    <li>• <strong>Nút "Tải Lại Trang":</strong> Reload trang ngay lập tức</li>
                    <li>• <strong>Shake Animation:</strong> Khi user cố gắng đóng bằng cách không được phép</li>
                    <li>• <strong>Visual Enhancement:</strong> Border đỏ, background tối hơn, icon animate</li>
                    <li>• <strong>Responsive Design:</strong> Hoạt động tốt trên mobile</li>
                </ul>
            </div>
        </div>

        <!-- Test Steps -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">
                🧪 Các Bước Test
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-blue-600">Test 1: Manual Dialog</h3>
                    <ol class="space-y-2 text-gray-700">
                        <li>1. Click "Show Persistent Dialog"</li>
                        <li>2. Thử click outside dialog → Shake</li>
                        <li>3. Thử bấm ESC → Shake</li>
                        <li>4. Click nút X để đóng</li>
                    </ol>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-orange-600">Test 2: Auto Logout</h3>
                    <ol class="space-y-2 text-gray-700">
                        <li>1. Click "Login Test"</li>
                        <li>2. Click "Test Auto Logout (10s)"</li>
                        <li>3. Đợi 10 giây KHÔNG chạm gì</li>
                        <li>4. Dialog sẽ xuất hiện và persistent</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Status Display -->
        <div class="card">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">
                📊 Status & Logs
            </h2>
            
            <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-sm text-gray-600">Auth Status</div>
                        <div id="authStatus" class="font-bold text-lg">Not Logged In</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-600">Dialog Status</div>
                        <div id="dialogStatus" class="font-bold text-lg">None</div>
                    </div>
                    <div>
                        <div class="text-sm text-gray-600">Timer Status</div>
                        <div id="timerStatus" class="font-bold text-lg">Inactive</div>
                    </div>
                </div>
            </div>
            
            <div id="logs" class="logs">
                <div>🚀 Persistent Dialog Test initialized...</div>
                <div>📝 Ready to test session expiry dialogs</div>
            </div>
            
            <div class="mt-4 text-center">
                <button onclick="clearLogs()" class="btn btn-secondary">
                    🧹 Clear Logs
                </button>
            </div>
        </div>
    </div>

    <!-- Include Auth System -->
    <script src="js/auth.js"></script>

    <script>
        let statusInterval;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('📱 Persistent Dialog Test page loaded');
            updateStatus();
            startStatusMonitoring();
        });

        // Log function
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        // Update status display
        function updateStatus() {
            const authStatus = document.getElementById('authStatus');
            const dialogStatus = document.getElementById('dialogStatus');
            const timerStatus = document.getElementById('timerStatus');

            // Auth status
            if (typeof auth !== 'undefined' && auth.isAuthenticated) {
                authStatus.textContent = `Logged In (${auth.user?.email || 'Unknown'})`;
                authStatus.className = 'font-bold text-lg text-green-600';
            } else {
                authStatus.textContent = 'Not Logged In';
                authStatus.className = 'font-bold text-lg text-red-600';
            }

            // Dialog status
            const existingDialog = document.getElementById('sessionExpiredModal');
            if (existingDialog) {
                dialogStatus.textContent = 'Persistent Dialog Active';
                dialogStatus.className = 'font-bold text-lg text-red-600';
            } else {
                dialogStatus.textContent = 'No Dialog';
                dialogStatus.className = 'font-bold text-lg text-gray-600';
            }

            // Timer status
            if (typeof auth !== 'undefined' && auth.activityTimer) {
                timerStatus.textContent = 'Active';
                timerStatus.className = 'font-bold text-lg text-green-600';
            } else {
                timerStatus.textContent = 'Inactive';
                timerStatus.className = 'font-bold text-lg text-gray-600';
            }
        }

        // Start status monitoring
        function startStatusMonitoring() {
            if (statusInterval) clearInterval(statusInterval);
            statusInterval = setInterval(updateStatus, 1000);
        }

        // Test login
        async function testLogin() {
            log('🔐 Testing login...');
            try {
                if (typeof auth === 'undefined') {
                    log('❌ Auth object not found!');
                    return;
                }
                
                const result = await auth.login('<EMAIL>', '123456');
                log('✅ Login successful!');
                log(`👤 User: ${result.khach_hang.email}`);
                
            } catch (error) {
                log(`❌ Login failed: ${error.message}`);
            }
        }

        // Show persistent dialog manually
        function showPersistentDialog() {
            log('🚨 Manually showing persistent session expired dialog...');
            
            if (typeof auth !== 'undefined') {
                auth.showSessionExpiredDialog();
                log('🔒 Persistent dialog shown - try to close it!');
                log('🚫 Try: Click outside, press ESC, then use X button');
            } else {
                log('❌ Auth object not available');
            }
        }

        // Test auto logout with short timeout
        function testAutoLogout() {
            if (typeof auth === 'undefined' || !auth.isAuthenticated) {
                log('❌ Please login first to test auto logout');
                return;
            }

            log('⏰ Starting auto logout test (10 seconds)...');
            log('🚫 Do NOT move mouse or touch screen for 10 seconds!');
            
            // Temporarily override timeout
            const originalTimeout = auth.INACTIVITY_TIMEOUT;
            auth.INACTIVITY_TIMEOUT = 10000; // 10 seconds
            
            // Restart activity tracking
            auth.stopActivityTracking();
            auth.startActivityTracking();
            
            log('⏳ Countdown started... wait for persistent dialog');
            
            // Restore original timeout after test
            setTimeout(() => {
                auth.INACTIVITY_TIMEOUT = originalTimeout;
                log('🔄 Timeout restored to original value');
            }, 15000);
        }

        // Clear all data
        function clearAll() {
            if (typeof auth !== 'undefined') {
                auth.clearAuthData();
                log('🗑️ All auth data cleared');
            }
            
            // Remove any existing dialogs
            const existingDialog = document.getElementById('sessionExpiredModal');
            if (existingDialog) {
                existingDialog.remove();
                log('🗑️ Existing dialog removed');
            }
        }

        // Clear logs
        function clearLogs() {
            document.getElementById('logs').innerHTML = '<div>🚀 Logs cleared...</div>';
        }

        // Listen for auth changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'user' || e.key === 'token') {
                log('📡 Auth state changed (storage event)');
            }
        });
    </script>
</body>
</html>
