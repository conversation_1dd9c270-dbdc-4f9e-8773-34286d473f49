{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 00:40:58"}
{"level":"error","message":"Failed to start server: db.execute is not a function","service":"restaurant-api","stack":"TypeError: db.execute is not a function\n    at CustomerModel.createTable (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:19:22)\n    at App.start (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\app.js:205:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-16 00:40:58"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 00:43:09"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 00:49:23"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 00:52:10"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 01:01:37"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 01:21:24"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 13:02:50"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 13:11:24"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-06-16 13:45:18"}
{"code":"ECONNREFUSED","errno":-111,"level":"error","message":"Database connection failed: connect ECONNREFUSED 127.0.0.1:3306","service":"restaurant-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:3306\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:28:40)\n    at register (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:133:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-04 11:16:06"}
{"code":"ECONNREFUSED","errno":-111,"level":"error","message":"Registration error: connect ECONNREFUSED 127.0.0.1:3306","service":"restaurant-api","stack":"Error: connect ECONNREFUSED 127.0.0.1:3306\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:28:40)\n    at register (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:133:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-04 11:16:06"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:46:11"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:46:40"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:46:47"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:48:08"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:48:17"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:48:18"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:49:13"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:50:01"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:54:06"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 13:58:12"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:13:37"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:14:32"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:16:06"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:17:26"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at process.processImmediate (node:internal/timers:459:9)","timestamp":"2025-07-05 14:17:28"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at process.processImmediate (node:internal/timers:459:9)","timestamp":"2025-07-05 14:17:29"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)\n    at runNextTicks (node:internal/process/task_queues:65:5)\n    at process.processImmediate (node:internal/timers:459:9)","timestamp":"2025-07-05 14:17:30"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:17:30"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:19:25"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:19:38"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:27:35"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:28:41"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:28:41"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-05 14:29:52"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-05 14:29:52"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-05 14:29:53"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-05 14:29:53"}
{"level":"error","message":"Login error: jwt is not defined","service":"restaurant-api","stack":"ReferenceError: jwt is not defined\n    at login (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\middleware\\auth.js:250:27)","timestamp":"2025-07-05 14:31:44"}
{"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async AIService.generateGeminiResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:117:22)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:90:16)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 09:55:16"}
{"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async AIService.generateGeminiResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:117:22)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:90:16)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 09:55:16"}
{"error":{"error":{"code":"invalid_api_key","message":"Invalid API Key","type":"invalid_request_error"}},"headers":{"alt-svc":"h3=\":443\"; ma=86400","cache-control":"private, max-age=0, no-store, no-cache, must-revalidate","cf-cache-status":"DYNAMIC","cf-ray":"95ccc8f4ca341114-HKG","connection":"keep-alive","content-length":"96","content-type":"application/json","date":"Thu, 10 Jul 2025 02:55:55 GMT","server":"cloudflare","set-cookie":"__cf_bm=6RrLwK6G2kiMfw1V2RMPM18jcFhGL2jNpDgsiBrgXmg-1752116155-*******-kH0kwctprZG6lP09rli.LMl.ltxiDGpmxBA_askW3nT_3Q54Iicvn3iSOZd.FJuDj6C3zxnkMl6vsnXPr8sWpTFNC72Dd2ls5v6bq.UD4do; path=/; expires=Thu, 10-Jul-25 03:25:55 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None","vary":"Origin","via":"1.1 google","x-groq-region":"bom","x-request-id":"req_01jzs45n9tf3fbt8zfftevfadb"},"level":"error","message":"Groq API Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}","service":"restaurant-api","stack":"Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}\n    at APIError.generate (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/error.js:43:20)\n    at Groq.makeStatusError (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:291:33)\n    at Groq.makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:335:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIService.generateGroqResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:144:26)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:84:18)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":401,"timestamp":"2025-07-10 09:55:55"}
{"error":{"error":{"code":"invalid_api_key","message":"Invalid API Key","type":"invalid_request_error"}},"headers":{"alt-svc":"h3=\":443\"; ma=86400","cache-control":"private, max-age=0, no-store, no-cache, must-revalidate","cf-cache-status":"DYNAMIC","cf-ray":"95ccc8f4ca341114-HKG","connection":"keep-alive","content-length":"96","content-type":"application/json","date":"Thu, 10 Jul 2025 02:55:55 GMT","server":"cloudflare","set-cookie":"__cf_bm=6RrLwK6G2kiMfw1V2RMPM18jcFhGL2jNpDgsiBrgXmg-1752116155-*******-kH0kwctprZG6lP09rli.LMl.ltxiDGpmxBA_askW3nT_3Q54Iicvn3iSOZd.FJuDj6C3zxnkMl6vsnXPr8sWpTFNC72Dd2ls5v6bq.UD4do; path=/; expires=Thu, 10-Jul-25 03:25:55 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None","vary":"Origin","via":"1.1 google","x-groq-region":"bom","x-request-id":"req_01jzs45n9tf3fbt8zfftevfadb"},"level":"error","message":"AI Service Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}","service":"restaurant-api","stack":"Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}\n    at APIError.generate (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/error.js:43:20)\n    at Groq.makeStatusError (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:291:33)\n    at Groq.makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:335:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIService.generateGroqResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:144:26)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:84:18)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":401,"timestamp":"2025-07-10 09:55:55"}
{"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async AIService.generateGeminiResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:117:22)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:90:16)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:01:13"}
{"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async AIService.generateGeminiResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:117:22)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:90:16)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:01:13"}
{"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:117:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:90:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:03:14"}
{"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:117:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:90:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:03:14"}
{"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:117:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:90:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:03:51"}
{"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [503 Service Unavailable] The model is overloaded. Please try again later.\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:117:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:90:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:03:51"}
{"error":{"error":{"code":"invalid_api_key","message":"Invalid API Key","type":"invalid_request_error"}},"headers":{"alt-svc":"h3=\":443\"; ma=86400","cache-control":"private, max-age=0, no-store, no-cache, must-revalidate","cf-cache-status":"DYNAMIC","cf-ray":"95cce73bcb8b045a-HKG","connection":"keep-alive","content-length":"96","content-type":"application/json","date":"Thu, 10 Jul 2025 03:16:35 GMT","server":"cloudflare","set-cookie":"__cf_bm=zp9q2Fe.116nxZp.WVKDmRhs_65d47scK2.d5lIydvs-1752117395-*******-r7OOQuiO6ZqHgWFnQXwmFICfa_yVSw.bgunpfYuN8jZazFNl4CI0Y94Pq.b3kotMUh2rIXm9GrPFThSKF.R3uCWzFIkQhljSzEy2IRkEB1c; path=/; expires=Thu, 10-Jul-25 03:46:35 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None","vary":"Origin","via":"1.1 google","x-groq-region":"bom","x-request-id":"req_01jzs5bgdef2n8gr1cjtqwe917"},"level":"error","message":"Groq API Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}","service":"restaurant-api","stack":"Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}\n    at APIError.generate (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/error.js:43:20)\n    at Groq.makeStatusError (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:291:33)\n    at Groq.makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:335:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIService.generateGroqResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:175:26)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:115:18)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":401,"timestamp":"2025-07-10 10:16:36"}
leResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:117:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:90:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":503,"statusText":"Service Unavailable","timestamp":"2025-07-10 10:15:48"}
{"error":{"error":{"code":"invalid_api_key","message":"Invalid API Key","type":"invalid_request_error"}},"headers":{"alt-svc":"h3=\":443\"; ma=86400","cache-control":"private, max-age=0, no-store, no-cache, must-revalidate","cf-cache-status":"DYNAMIC","cf-ray":"95cce73bcb8b045a-HKG","connection":"keep-alive","content-length":"96","content-type":"application/json","date":"Thu, 10 Jul 2025 03:16:35 GMT","server":"cloudflare","set-cookie":"__cf_bm=zp9q2Fe.116nxZp.WVKDmRhs_65d47scK2.d5lIydvs-1752117395-*******-r7OOQuiO6ZqHgWFnQXwmFICfa_yVSw.bgunpfYuN8jZazFNl4CI0Y94Pq.b3kotMUh2rIXm9GrPFThSKF.R3uCWzFIkQhljSzEy2IRkEB1c; path=/; expires=Thu, 10-Jul-25 03:46:35 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None","vary":"Origin","via":"1.1 google","x-groq-region":"bom","x-request-id":"req_01jzs5bgdef2n8gr1cjtqwe917"},"level":"error","message":"AI Service Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}","service":"restaurant-api","stack":"Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}\n    at APIError.generate (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/error.js:43:20)\n    at Groq.makeStatusError (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:291:33)\n    at Groq.makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:335:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIService.generateGroqResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:175:26)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:115:18)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":401,"timestamp":"2025-07-10 10:16:36"}
{"error":{"error":{"code":"invalid_api_key","message":"Invalid API Key","type":"invalid_request_error"}},"headers":{"alt-svc":"h3=\":443\"; ma=86400","cache-control":"private, max-age=0, no-store, no-cache, must-revalidate","cf-cache-status":"DYNAMIC","cf-ray":"95ccecd2cf9ae2ff-HKG","connection":"keep-alive","content-length":"96","content-type":"application/json","date":"Thu, 10 Jul 2025 03:20:24 GMT","server":"cloudflare","set-cookie":"__cf_bm=HA.****************************************-1752117624-*******-kVqmvQD88CU2aeCGT3jiPCuTIOc__ki76An1dvKbc.mFtTKjsUFbUSy7bzjIQiIbV7XA2pXt2JGTjhWCxQKepJVwFLF4SLdfFnKfb0IIkho; path=/; expires=Thu, 10-Jul-25 03:50:24 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None","vary":"Origin","via":"1.1 google","x-groq-region":"bom","x-request-id":"req_01jzs5jfzvffdtg5cj808cs3xq"},"level":"error","message":"Groq API Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}","service":"restaurant-api","stack":"Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}\n    at APIError.generate (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/error.js:43:20)\n    at Groq.makeStatusError (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:291:33)\n    at Groq.makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:335:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIService.generateGroqResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:135:26)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:75:18)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":401,"timestamp":"2025-07-10 10:20:24"}
{"error":{"error":{"code":"invalid_api_key","message":"Invalid API Key","type":"invalid_request_error"}},"headers":{"alt-svc":"h3=\":443\"; ma=86400","cache-control":"private, max-age=0, no-store, no-cache, must-revalidate","cf-cache-status":"DYNAMIC","cf-ray":"95ccecd2cf9ae2ff-HKG","connection":"keep-alive","content-length":"96","content-type":"application/json","date":"Thu, 10 Jul 2025 03:20:24 GMT","server":"cloudflare","set-cookie":"__cf_bm=HA.****************************************-1752117624-*******-kVqmvQD88CU2aeCGT3jiPCuTIOc__ki76An1dvKbc.mFtTKjsUFbUSy7bzjIQiIbV7XA2pXt2JGTjhWCxQKepJVwFLF4SLdfFnKfb0IIkho; path=/; expires=Thu, 10-Jul-25 03:50:24 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None","vary":"Origin","via":"1.1 google","x-groq-region":"bom","x-request-id":"req_01jzs5jfzvffdtg5cj808cs3xq"},"level":"error","message":"AI Service Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}","service":"restaurant-api","stack":"Error: 401 {\"error\":{\"message\":\"Invalid API Key\",\"type\":\"invalid_request_error\",\"code\":\"invalid_api_key\"}}\n    at APIError.generate (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/error.js:43:20)\n    at Groq.makeStatusError (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:291:33)\n    at Groq.makeRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/groq-sdk/core.js:335:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async AIService.generateGroqResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:135:26)\n    at async AIService.generateChatResponse (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/services/AIService.js:75:18)\n    at async /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/controllers/ChatController.js:46:26","status":401,"timestamp":"2025-07-10 10:20:24"}
{"level":"error","message":"❌ No AI services available. Please configure API keys.","service":"restaurant-api","timestamp":"2025-07-11 11:54:16"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Object.createConnectionPromise [as createConnection] (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\mysql2\\promise.js:19:31)\n    at CustomerModel.getConnection (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:18:44)\n    at CustomerModel.createTable (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:28:39)\n    at App.start (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\app.js:204:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-13 13:59:58"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Object.createConnectionPromise [as createConnection] (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\mysql2\\promise.js:19:31)\n    at CustomerModel.getConnection (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:18:44)\n    at CustomerModel.createTable (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:28:39)\n    at App.start (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\app.js:204:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-14 09:18:55"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Object.createConnectionPromise [as createConnection] (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\mysql2\\promise.js:19:31)\n    at CustomerModel.getConnection (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:18:44)\n    at CustomerModel.createTable (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:28:39)\n    at App.start (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\app.js:204:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-14 09:51:56"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Object.createConnectionPromise [as createConnection] (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\mysql2\\promise.js:19:31)\n    at CustomerModel.getConnection (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:18:44)\n    at CustomerModel.createTable (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\models\\CustomerModel.js:28:39)\n    at App.start (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\app.js:204:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-14 10:21:35"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:53:16"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:53:16"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:55"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:55"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:57"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:57"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:57"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:57"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:58"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:58"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:58"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:56:58"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:03"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"code":"ER_ACCESS_DENIED_NO_PASSWORD_ERROR","errno":1698,"level":"error","message":"Login error: Access denied for user 'root'@'localhost'","service":"restaurant-api","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost'\n    at Object.createConnectionPromise [as createConnection] (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/mysql2/promise.js:19:31)\n    at getConnection (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:29:40)\n    at login (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/middleware/auth.js:222:38)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at next (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:157:13)\n    at Route.dispatch (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/route.js:117:3)\n    at handle (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:435:11)\n    at Layer.handleRequest (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/lib/layer.js:152:17)\n    at /mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:295:15\n    at processParams (/mnt/hgfs/DuAnCNTT/DuAnBaoCaoWeb/backend/node_modules/router/index.js:582:12)","timestamp":"2025-07-16 22:57:04"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:113:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:81:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":400,"statusText":"Bad Request","timestamp":"2025-07-17 10:50:07"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:113:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:81:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":400,"statusText":"Bad Request","timestamp":"2025-07-17 10:50:07"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:113:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:81:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":400,"statusText":"Bad Request","timestamp":"2025-07-17 10:50:22"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:113:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:81:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":400,"statusText":"Bad Request","timestamp":"2025-07-17 10:50:22"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"error","message":"Gemini API Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:113:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:81:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":400,"statusText":"Bad Request","timestamp":"2025-07-17 10:50:46"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","domain":"googleapis.com","metadata":{"service":"generativelanguage.googleapis.com"},"reason":"API_KEY_INVALID"},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"API key not valid. Please pass a valid API key."}],"level":"error","message":"AI Service Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]","service":"restaurant-api","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent: [400 Bad Request] API key not valid. Please pass a valid API key. [{\"@type\":\"type.googleapis.com/google.rpc.ErrorInfo\",\"reason\":\"API_KEY_INVALID\",\"domain\":\"googleapis.com\",\"metadata\":{\"service\":\"generativelanguage.googleapis.com\"}},{\"@type\":\"type.googleapis.com/google.rpc.LocalizedMessage\",\"locale\":\"en-US\",\"message\":\"API key not valid. Please pass a valid API key.\"}]\n    at handleResponseNotOk (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:403:9)\n    at async generateContent (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\node_modules\\@google\\generative-ai\\dist\\index.js:867:22)\n    at async AIService.generateGeminiResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:113:22)\n    at async AIService.generateChatResponse (D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\services\\AIService.js:81:16)\n    at async D:\\DuAnCNTT\\DuAnBaoCaoWeb\\backend\\controllers\\ChatController.js:46:26","status":400,"statusText":"Bad Request","timestamp":"2025-07-17 10:50:46"}
