<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thực <PERSON> - <PERSON><PERSON> Ẩm <PERSON>hự<PERSON></title>
    <link rel="icon" href="img/logoPN.png" type="image/png">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Dancing+Script:wght@600;700&display=swap" rel="stylesheet">
    <style>
        :root { --primary-color: #e53e3e; }
        body { font-family: 'Roboto', sans-serif; }
        .font-cursive { font-family: 'Dancing Script', cursive; }
        .bg-primary { background-color: var(--primary-color); }
        .text-primary { color: var(--primary-color); }
        .menu-item:hover { transform: translateY(-5px); }
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
        input[type=number] { -moz-appearance: textfield; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4 py-2 flex justify-between items-center">
            <a href="Index.html" class="flex items-center">
                <div class="text-2xl font-bold text-primary mr-2"><span class="font-cursive">Phương Nam</span></div>
                <div class="text-sm text-gray-700">Ẩm Thực</div>
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="Index.html" class="nav-link">Trang Chủ</a>
                <a href="gioithieu.html" class="nav-link">Giới Thiệu</a>
                <a href="Menu.html" class="nav-link active">Thực Đơn</a>
                <a href="lienhe&datban.html" class="nav-link">Đặt Bàn & Liên Hệ</a>
            </nav>
            <div class="flex items-center">
                <button id="cartBtn" class="relative">
                    <i class="fas fa-shopping-cart text-gray-600 text-xl"></i>
                    <span class="absolute -top-2 -right-2 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">0</span>
                </button>
                <button id="menuToggle" class="md:hidden ml-4">
                    <i class="fas fa-bars text-2xl text-gray-600"></i>
                </button>
            </div>
        </div>
        <!-- Mobile Navigation -->
        <nav id="mobileNav" class="md:hidden hidden mt-2 pb-4">
            <div class="flex flex-col space-y-2 px-4">
                <a href="Index.html" class="py-2 hover:bg-gray-100 rounded">Trang Chủ</a>
                <a href="gioithieu.html" class="py-2 hover:bg-gray-100 rounded">Giới Thiệu</a>
                <a href="Menu.html" class="py-2 bg-gray-100 rounded">Thực Đơn</a>
                <a href="lienhe&datban.html" class="py-2 hover:bg-gray-100 rounded">Đặt Bàn & Liên Hệ</a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 py-8">
        <h2 class="text-3xl font-bold mb-6 text-center text-gray-800">Thực Đơn Của Chúng Tôi</h2>
        <!-- Search Bar -->
        <div class="w-full max-w-2xl mx-auto mb-10 flex items-center gap-4 bg-white p-4 rounded-lg shadow">
            <input id="searchInput" type="text" placeholder="Tìm kiếm món ăn..." class="flex-grow px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
            <div class="text-gray-600 whitespace-nowrap">Tìm thấy: <span id="resultCount" class="font-semibold">0</span> món</div>
        </div>
        <!-- Menu Grid -->
        <div id="menuGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"></div>
        <!-- No Results -->
        <div id="noResult" class="hidden text-center text-gray-500 mt-8">
            <i class="fas fa-search text-4xl mb-4"></i>
            <p>Không tìm thấy món ăn phù hợp.</p>
        </div>
    </main>

    <script>
        const API_BASE = 'http://localhost:3000';
        const PLACEHOLDER = `${API_BASE}/images/placeholder.png`;

        // Function to check if user is logged in
        function checkUserLogin() {
            const userData = localStorage.getItem('userData') || localStorage.getItem('user');
            if (userData) {
                try {
                    const user = JSON.parse(userData);
                    return user && user.id;
                } catch (error) {
                    console.error('Error parsing user data:', error);
                    return false;
                }
            }
            return false;
        }

        // Function to show login required notification
        function showLoginRequired() {
            alert('Vui lòng đăng nhập để thêm món ăn vào giỏ hàng!');

            // Try to open login modal if available
            const loginModal = document.getElementById('loginModal');
            if (loginModal) {
                localStorage.setItem('redirectAfterLogin', 'menu');
                loginModal.classList.add('active');
            } else {
                // Redirect to login page
                if (confirm('Chuyển đến trang đăng nhập?')) {
                    window.location.href = 'Index-new.html';
                }
            }
        }

        // Cart (Assuming this logic is correct for your needs)
        const cart = {
            items: JSON.parse(localStorage.getItem('cart') || '[]'),
            add(item, qty) {
                // Check if user is logged in before adding to cart
                if (!checkUserLogin()) {
                    showLoginRequired();
                    return false;
                }

                // Find the item *as returned by the API* in the cart
                const exist = this.items.find(i => i.id_mon === item.id_mon);
                // Get the max quantity *from the API item*
                const maxQty = item.so_luong;

                if (exist) {
                    // Ensure we don't exceed max quantity
                    exist.qty = Math.min(exist.qty + qty, maxQty);
                } else {
                    // Add the item *from the API* plus the desired quantity
                    // Make sure qty doesn't exceed maxQty on initial add
                    this.items.push({ ...item, qty: Math.min(qty, maxQty) });
                }
                this.save();
                this.updateUI();

                // Show success notification
                alert(`Đã thêm "${item.ten_mon}" vào giỏ hàng!`);
                return true;
            },
            save() { localStorage.setItem('cart', JSON.stringify(this.items)); },
            updateUI() {
                const total = this.items.reduce((s, i) => s + i.qty, 0);
                const cartBtn = document.querySelector('#cartBtn span');
                if (cartBtn) {
                    cartBtn.textContent = total;
                }
            }
        };

        // Fetch menu
        async function fetchMenu(searchTerm = '') {
          try {
            const response = await fetch(`http://localhost:3000/api/foods?search=${encodeURIComponent(searchTerm)}`);

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            // Transform API response to expected format
            if (result.success && result.data) {
              return result.data;
            } else {
              throw new Error('Invalid API response format');
            }
          } catch (error) {
            console.error('Error fetching menu:', error);
            throw error;
          }
        }

        // Render menu
        function renderMenu(items = []) {
            const grid = document.getElementById('menuGrid');
            const noRes = document.getElementById('noResult');
            document.getElementById('resultCount').textContent = items.length;

            if (!items.length) {
                noRes.classList.remove('hidden');
                grid.innerHTML = '';
                return;
            }
            noRes.classList.add('hidden');

            grid.innerHTML = items.map(it => {
                const out = !it.so_luong;
                const stockCls = out ? 'bg-gray-500' : it.so_luong <= 5 ? 'bg-red-500 animate-pulse' : 'bg-green-500';

                // 👉 DÙNG THẲNG URL từ API hoặc placeholder
                const imgSrc = it.hinh_anh || PLACEHOLDER;

                // Format giá
                const price = Number(it.gia);
                const formattedPrice = !isNaN(price) ? price.toLocaleString('vi-VN') + 'đ' : 'N/A';

                // Escape JSON cho onclick (nếu còn dùng)
                const itemJson = JSON.stringify(it).replace(/"/g, '&quot;');

                return `
                <div class="menu-item bg-white rounded-lg shadow-lg transition transform hover:-translate-y-1 flex flex-col ${out?'opacity-60':''}">
                    <div class="h-64 relative bg-gray-200">
                        <img src="${imgSrc}" alt="${it.ten_mon}" class="w-full h-full object-cover">
                        <div class="absolute top-2 right-2 ${stockCls} text-white px-2 py-1 rounded-full text-xs">
                            ${out ? 'Hết hàng' : `Còn ${it.so_luong} phần`}
                        </div>
                    </div>
                    <div class="p-5 flex flex-col flex-grow">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="font-bold text-xl">${it.ten_mon || 'Chưa đặt tên'}</h3>
                            <span class="text-primary font-bold text-lg">${formattedPrice}</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4 flex-grow">${it.mo_ta || 'Chưa có mô tả'}</p>
                        ${!out ? `
                        <div class="flex items-center justify-between gap-2 mb-3">
                            <span class="text-sm text-gray-500">Số lượng:</span>
                            <div class="flex border rounded">
                                <button onclick="changeQty(${it.id_mon}, -1)" class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-l">-</button>
                                <input id="qty-${it.id_mon}" type="number" min="1" max="${it.so_luong}" value="1" class="w-12 text-center border-y">
                                <button onclick="changeQty(${it.id_mon}, 1)" class="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-r">+</button>
                            </div>
                        </div>
                        <button onclick='cart.add(${itemJson}, parseInt(document.getElementById("qty-${it.id_mon}").value))' class="w-full bg-primary text-white py-2.5 rounded-lg hover:bg-red-700 flex items-center justify-center gap-2">
                            <i class="fas fa-cart-plus"></i> Thêm vào giỏ
                        </button>
                        ` : '<p class="text-center text-gray-500 text-sm mt-auto pt-4">Món này đã hết hàng</p>'}
                    </div>
                </div>`;
            }).join('');
        }

        function changeQty(id, delta) {
            const inp = document.getElementById(`qty-${id}`);
            if (!inp) return;
            const currentVal = parseInt(inp.value, 10);
            const maxVal = parseInt(inp.max, 10);
            let newVal = currentVal + delta;
            // Clamp value between 1 and max
            newVal = Math.max(1, Math.min(newVal, maxVal));
            inp.value = newVal;
        }

        // Init
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile Menu Toggle
            const menuToggle = document.getElementById('menuToggle');
            const mobileNav = document.getElementById('mobileNav');
            if(menuToggle && mobileNav) {
                menuToggle.addEventListener('click', () => mobileNav.classList.toggle('hidden'));
            } else {
                console.error("Could not find menu toggle or mobile nav elements.");
            }

            // Search Input Handling
            const searchInput = document.getElementById('searchInput');
            if(searchInput) {
                searchInput.value = new URLSearchParams(location.search).get('search') || '';
                let searchTimeoutId;
                searchInput.addEventListener('input', e => {
                    clearTimeout(searchTimeoutId);
                    const searchTerm = e.target.value.trim();
                    searchTimeoutId = setTimeout(() => {
                        loadMenu(searchTerm);
                        // Update URL without reloading page
                        const newUrl = searchTerm ? `${location.pathname}?search=${encodeURIComponent(searchTerm)}` : location.pathname;
                        history.pushState({ search: searchTerm }, '', newUrl);
                    }, 300); // Debounce search input
                });
            } else {
                console.error("Could not find search input element.");
            }

            // Cart Button Click Handler
            const cartBtn = document.getElementById('cartBtn');
            if (cartBtn) {
                cartBtn.addEventListener('click', () => {
                    if (!checkUserLogin()) {
                        showLoginRequired();
                        return;
                    }

                    // If user is logged in, show cart (you can implement cart modal here)
                    alert('Giỏ hàng của bạn có ' + cart.items.length + ' món ăn');
                    // TODO: Implement proper cart modal display
                });
            }

            // Initial Load
            loadMenu(searchInput ? searchInput.value : '');
            cart.updateUI(); // Update cart counter on load

            // Handle browser back/forward navigation for search state
            window.addEventListener('popstate', (event) => {
                const searchTerm = event.state?.search || '';
                if (searchInput) {
                    searchInput.value = searchTerm;
                }
                loadMenu(searchTerm);
            });
        });

        async function loadMenu(search = '') {
          const menuGrid = document.getElementById('menuGrid');
          const noResultDiv = document.getElementById('noResult');
          
          menuGrid.innerHTML = `
            <div class="col-span-full text-center py-10">
              <i class="fas fa-spinner fa-spin text-4xl mb-4"></i>
              <p>Đang tải thực đơn...</p>
            </div>
          `;
          noResultDiv.classList.add('hidden'); // Hide no results during load

          try {
            const data = await fetchMenu(search);
            renderMenu(data);
          } catch (e) {
            console.error("Error loading menu:", e);
            menuGrid.innerHTML = `
              <div class="col-span-full text-center text-gray-500 py-10">
                <i class="fas fa-exclamation-triangle text-4xl mb-4 text-red-500"></i>
                <p>Có lỗi khi tải thực đơn. ${e.message || e}</p>
                <button onclick="loadMenu('${search}')" class="mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-red-700">
                  <i class="fas fa-redo"></i> Thử lại
                </button>
              </div>
            `;
          }
        }
    </script>
</body>
</html>
