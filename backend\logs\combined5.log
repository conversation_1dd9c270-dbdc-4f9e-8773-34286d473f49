{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:38","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:29:38","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:39","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:29:39","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:39","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:29:39","url":"/api/foods?limit=50&offset=0"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:29:39","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:39","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:39","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/banhdacua_thumb.jpg"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/banhkhot_thumb.jpg"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/banhxeo_thumb.jpg"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/chacalavong_thumb.jpg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/chagioPN_thumb.jpg"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/comtam_thumb.webp"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/goingosen_thumb.jpg"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/lauduoibo_thumb.jpg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:40","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:40","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:43","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:29:43","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:44","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:29:44","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:44","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:29:44","url":"/api/foods?limit=50&offset=0"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:29:44","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:44","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:44","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:44","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:44","url":"/images/banhcan_thumb.jpg"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:44","url":"/images/banhdacua_thumb.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:44","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/banhkhot_thumb.jpg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/banhxeo_thumb.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/bunthitnuong_thumb.jpg"}
{"duration":"139ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/canhchuacaloc_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:45","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/chagioPN_thumb.jpg"}
{"duration":"37ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/chaoluong_thumb.jpg"}
{"duration":"53ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:45","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:46","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:46","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:46","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:46","url":"/images/comtam_thumb.webp"}
{"duration":"37ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:46","url":"/images/ganuongmuoiot_thumb.png"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:46","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:46","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:46","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/myquang_thumb.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:29:47","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:29:47","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:21","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:30:21","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:22","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:30:22","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:22","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:30:22","url":"/api/foods?limit=50&offset=0"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:30:22","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:22","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:22","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:22","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:22","url":"/images/banhcan_thumb.jpg"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:22","url":"/images/banhdacua_thumb.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:22","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:22","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/banhkhot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/banhxeo_thumb.jpg"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/cakhoto_thumb.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/chacalavong_thumb.jpg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/comtam_thumb.webp"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"73ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/lauduoibo_thumb.jpg"}
{"duration":"100ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/laumam_thumb.webp"}
{"duration":"105ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/supcua_thumb.jpg"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/nemnuong_thumb.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:30:23","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:30:23","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:01","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:01","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:02","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:02","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:02","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:02","url":"/api/foods?limit=50&offset=0"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:33:02","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/banhcan_thumb.jpg"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/banhdacua_thumb.jpg"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/banhkhot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/banhxeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/bunrieucua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/canhchuacaloc_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:03","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/comgahoian_thumb.jpeg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:03","url":"/images/comtam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/lauduoibo_thumb.jpg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"48ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:04","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:04","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:23","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:23","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:24","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:24","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:24","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:24","url":"/api/foods?limit=50&offset=0"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:33:24","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/banhcan_thumb.jpg"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/banhdacua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/banhkhot_thumb.jpg"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/banhxeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"148ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/cakhoto_thumb.jpg"}
{"duration":"169ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"69ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/comgahoian_thumb.jpeg"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/comtam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/goicuon_thumb.jpg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/nemnuong_thumb.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:25","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:25","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:26","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:26","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:26","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:43","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:43","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:44","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:44","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:44","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:33:44","url":"/api/foods?limit=50&offset=0"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:33:44","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/banhdacua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/banhkhot_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/banhxeo_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"38ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/chacalavong_thumb.jpg"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/chagioPN_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/comtam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/goicuon_thumb.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:33:45","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:33:45","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:03","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:34:03","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:04","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:34:04","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:04","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:34:04","url":"/api/foods?limit=50&offset=0"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:34:04","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:04","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:04","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:04","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:04","url":"/images/banhcan_thumb.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/banhdacua_thumb.jpg"}
{"duration":"44ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/banhkhot_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/banhxeo_thumb.jpg"}
{"duration":"38ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/bunbohue_thumb.png"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/bunthitnuong_thumb.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/bunrieucua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/cakhoto_thumb.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/chagioPN_thumb.jpg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/comgahoian_thumb.jpeg"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/comtam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/goidudu_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/hutieunamvang_thumb.webp"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:34:05","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:34:05","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:14","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:37:14","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:15","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:37:15","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:15","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:37:15","url":"/api/foods?limit=50&offset=0"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:37:15","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:15","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:15","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:15","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:15","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:15","url":"/images/banhcan_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:15","url":"/images/banhdacua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:16","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:16","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:16","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:16","url":"/images/banhkhot_thumb.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:16","url":"/images/banhxeo_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:16","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:17","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:17","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:17","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:17","url":"/images/bunbohue_thumb.png"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:17","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:17","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:17","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:17","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:17","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"47ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:17","url":"/images/cakhoto_thumb.jpg"}
{"duration":"50ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:17","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"60ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/chagioPN_thumb.jpg"}
{"duration":"38ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"63ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/comtam_thumb.webp"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/nemnuong_thumb.jpg"}
{"duration":"60ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:18","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:18","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:40","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:37:40","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:41","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:37:41","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:41","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:37:41","url":"/api/foods?limit=50&offset=0"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:37:41","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:41","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/banhcan_thumb.jpg"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/banhdacua_thumb.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/banhkhot_thumb.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/banhxeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/cakhoto_thumb.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/chagioPN_thumb.jpg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:42","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/comgahoian_thumb.jpeg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/comtam_thumb.webp"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:42","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/goicuon_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/goidudu_thumb.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/laucakeo_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:37:43","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:37:43","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:02","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:02","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:02","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:03","url":"/images/banhhoiheoquay_high.jpg"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:03","url":"/images/banhcan_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:03","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"52ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:03","url":"/images/banhdacua_high.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:03","url":"/images/banhcan_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:03","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:03","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:03","url":"/images/banhdacua_med.jpg"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:03","url":"/images/banhhoiheoquay_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:03","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:03","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:03","url":"/images/banhhoiheoquay.jpg"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:03","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:03","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:03","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:05","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:05","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:05","url":"/api/foods?limit=50&offset=0"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:05","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:05","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:05","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"53ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:05","url":"/images/banhdacua_thumb.jpg"}
{"duration":"48ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:05","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"54ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:05","url":"/images/banhkhot_thumb.jpg"}
{"duration":"63ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhxeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhcan_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhcan_high.jpg"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhdacua_med.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhdacua_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhhoiheoquay_med.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:06","url":"/images/banhcan.jpg"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:06","url":"/images/banhdacua.jpg"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:06","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:06","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/bunbohue_thumb.png"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:06","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/banhkhot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/banhkhot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/banhxeo_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/banhkhot_med.jpg"}
{"duration":"44ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/banhkhot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/banhxeo_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"73ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/banhxeo_med.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/banhxeo_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bolalot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bolalot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bolalot_med.jpg"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bolalot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/banhkhot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"42ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/canhchuacaloc_thumb.jpg"}
{"duration":"37ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:07","url":"/images/banhkhot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/banhxeo.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:07","url":"/images/banhxeo.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:07","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunbohue_med.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunbohue_high.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bunbohue_med.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunrieucua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"39ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bunbohue_high.png"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bunrieucua_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunrieucua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunthitnuong_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunthitnuong_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bunrieucua_high.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bunthitnuong_med.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/bunthitnuong_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunbohue.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:07","url":"/images/chaoluong_thumb.jpg"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:07","url":"/images/bunbohue.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunthitnuong.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:07","url":"/images/bunrieucua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:07","url":"/images/bunthitnuong.jpg"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:07","url":"/images/bunrieucua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"POST","service":"restaurant-api","timestamp":"2025-07-13 15:38:11","url":"/api/chat","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"✅ Gemini 2.0 Flash response generated successfully","service":"restaurant-api","timestamp":"2025-07-13 15:38:13"}
{"dataLength":770,"level":"info","message":"API response","method":"POST","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:13","url":"/api/chat"}
{"duration":"2399ms","ip":"::1","level":"info","message":"Request completed","method":"POST","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:13","url":"/api/chat"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:13","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:13","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:14","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:14","url":"/api/foods?limit=50&offset=0"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:14","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhcan_thumb.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua_thumb.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"45ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhkhot_thumb.jpg"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhxeo_thumb.jpg"}
{"duration":"45ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhcan_med.jpg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhcan_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua_med.jpg"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua_high.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:14","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:14","url":"/images/banhhoiheoquay.jpg"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:14","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"POST","service":"restaurant-api","timestamp":"2025-07-13 15:38:39","url":"/api/khach_hang/login?t=1752395919670","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"User logged in successfully:","service":"restaurant-api","timestamp":"2025-07-13 15:38:40"}
{"duration":"1149ms","ip":"::1","level":"info","message":"Request completed","method":"POST","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:40","url":"/api/khach_hang/login?t=1752395919670"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:42","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:42","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:43","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:38:43","url":"/api/foods?limit=50&offset=0"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:43","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"56ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhcan_thumb.jpg"}
{"duration":"61ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"37ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhkhot_thumb.jpg"}
{"duration":"38ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhxeo_thumb.jpg"}
{"duration":"47ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhcan_med.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhcan_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua_med.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay_med.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:43","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:43","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:43","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:44","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:44","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:44","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:48","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:48","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:48","url":"/images/banhhoiheoquay.jpg"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:48","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:48","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:48","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:38:48","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:38:48","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:48","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:48","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:48","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:48","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:48","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:48","url":"/images/banhdacua.jpg"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:48","url":"/images/banhcan.jpg"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:48","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:51","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:39:51","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:51","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:39:51","url":"/api/foods?limit=50&offset=0"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:51","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:51","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:51","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:51","url":"/images/banhcan_thumb.jpg"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:51","url":"/images/banhdacua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:51","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:51","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhkhot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhxeo_thumb.jpg"}
{"duration":"60ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhcan_med.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhcan_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhdacua_med.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhdacua_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"115ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay_med.jpg"}
{"duration":"108ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:52","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:52","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:52","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:52","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:52","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:53","url":"/images/bunbohue_thumb.png"}
{"duration":"39ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:53","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"53ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:39:53","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:53","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:53","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:39:53","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:53","url":"/images/banhdacua.jpg"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:39:53","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:25","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:42:25","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:25","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:42:25","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:42:26","url":"/api/foods?limit=50&offset=0"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:26","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhkhot_thumb.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua_thumb.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhxeo_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhcan_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhcan_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua_med.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua_high.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:26","url":"/images/banhcan.jpg"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:26","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/bunbohue_thumb.png"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:26","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:26","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:26","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/banhkhot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/banhkhot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/banhxeo_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/banhkhot_med.jpg"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/banhkhot_high.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/banhxeo_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/banhxeo_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/bolalot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/bolalot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/banhxeo_high.jpg"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/bolalot_med.jpg"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/bolalot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:42:27","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/banhkhot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/banhxeo.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:27","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:27","url":"/images/banhkhot.jpg"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:27","url":"/images/banhxeo.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:27","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:42:34","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:42:34","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:43:47","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:43:47","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:44:16","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:44:16","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:46:01","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:46:01","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:46:20","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:46:20","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:46:59","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:46:59","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:22","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:22","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:23","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:58","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:47:58","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:58","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:47:58","url":"/api/foods?limit=50&offset=0"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:58","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"184ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua_thumb.jpg"}
{"duration":"193ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"253ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhkhot_thumb.jpg"}
{"duration":"255ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhxeo_thumb.jpg"}
{"duration":"214ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"74ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhcan_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhcan_high.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua_med.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay_med.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:59","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:59","url":"/images/banhdacua.jpg"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:47:59","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:48:02","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:48:02","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:48:27","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:48:27","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:48:43","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:48:43","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:49:01","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:49:01","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:49:39","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:50:19","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:50:19","url":"/api/foods?limit=50&offset=0"}
{"duration":"67ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:19","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"52ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhdacua_thumb.jpg"}
{"duration":"56ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"68ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhkhot_thumb.jpg"}
{"duration":"71ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhxeo_thumb.jpg"}
{"duration":"82ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhcan_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhcan_high.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhdacua_med.jpg"}
{"duration":"45ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhdacua_high.jpg"}
{"duration":"61ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhhoiheoquay_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:19","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:19","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:20","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:20","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:20","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:20","url":"/images/banhdacua.jpg"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:20","url":"/images/banhhoiheoquay.jpg"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:20","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/bunbohue_thumb.png"}
{"duration":"42ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"51ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/banhkhot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/banhkhot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/banhxeo_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/banhkhot_med.jpg"}
{"duration":"45ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/banhkhot_high.jpg"}
{"duration":"45ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/banhxeo_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/banhxeo_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/bolalot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/banhxeo_high.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/bolalot_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/bolalot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:50:21","url":"/images/bolalot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/banhkhot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:21","url":"/images/banhkhot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/banhxeo.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:21","url":"/images/banhxeo.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:21","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:21","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:50:22","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:50:22","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:51:52","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:51:52","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:52:09","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:52:09","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:52:38","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:52:38","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:04","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:04","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:53:33","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:53:33","url":"/api/foods?limit=50&offset=0"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:33","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"44ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhcan_thumb.jpg"}
{"duration":"54ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua_thumb.jpg"}
{"duration":"60ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"58ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhkhot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"71ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhxeo_thumb.jpg"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhcan_med.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhcan_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua_med.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua_high.jpg"}
{"duration":"47ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay_med.jpg"}
{"duration":"62ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:33","url":"/images/banhcan.jpg"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:33","url":"/images/banhdacua.jpg"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:33","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:36","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:36","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:36","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:36","url":"/images/bunbohue_thumb.png"}
{"duration":"42ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:36","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"60ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:36","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/banhkhot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/banhkhot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/banhxeo_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:37","url":"/images/banhkhot_med.jpg"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:37","url":"/images/banhkhot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/banhxeo_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/bolalot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/bolalot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"91ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:37","url":"/images/banhxeo_med.jpg"}
{"duration":"68ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:37","url":"/images/banhxeo_high.jpg"}
{"duration":"84ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:37","url":"/images/bolalot_med.jpg"}
{"duration":"87ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:53:37","url":"/images/bolalot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/banhkhot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:37","url":"/images/banhkhot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/banhxeo.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:37","url":"/images/banhxeo.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:37","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:37","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:43","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:43","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:43","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:43","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:53:43","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:53:43","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:15","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:15","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:15","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:15","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:15","url":"/images/banhdacua.jpg"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:15","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:36","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:36","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:36","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:36","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:36","url":"/images/banhdacua.jpg"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:36","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:53","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:53","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:53","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"3ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:53","url":"/images/banhdacua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:55:53","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:55:53","url":"/images/banhcan.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:44","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:56:44","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:56:45","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:56:45","url":"/api/foods?limit=50&offset=0"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:56:45","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"58ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/banhdacua_thumb.jpg"}
{"duration":"63ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/banhhoiheoquay_thumb.jpg"}
{"duration":"69ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/banhkhot_thumb.jpg"}
{"duration":"76ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/banhxeo_thumb.jpg"}
{"duration":"82ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:45","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:45","url":"/images/banhcan_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:46","url":"/images/banhdacua_high.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:46","url":"/images/banhhoiheoquay_med.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:46","url":"/images/banhcan_high.jpg"}
{"duration":"43ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:46","url":"/images/banhdacua_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:56:46","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:56:46","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:56:46","url":"/images/banhdacua.jpg"}
{"duration":"36ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:56:46","url":"/images/banhcan.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:56:46","url":"/images/banhhoiheoquay.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/bunbohue_thumb.png"}
{"duration":"41ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"55ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/banhkhot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/banhkhot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/banhxeo_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/banhxeo_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/bolalot_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/bolalot_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"47ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/banhkhot_med.jpg"}
{"duration":"51ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/banhkhot_high.jpg"}
{"duration":"52ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/banhxeo_med.jpg"}
{"duration":"51ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/banhxeo_high.jpg"}
{"duration":"52ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/bolalot_med.jpg"}
{"duration":"68ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/bolalot_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:33","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/banhkhot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/banhxeo.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:33","url":"/images/bolalot.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:33","url":"/images/banhkhot.jpg"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:33","url":"/images/banhxeo.jpg"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:33","url":"/images/bolalot.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunbohue_med.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunbohue_high.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunrieucua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/bunbohue_med.png"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/bunbohue_high.png"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/bunrieucua_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunrieucua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunthitnuong_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunthitnuong_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"38ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/bunrieucua_high.jpg"}
{"duration":"41ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/bunthitnuong_med.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/bunthitnuong_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunbohue.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:34","url":"/images/bunbohue.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunthitnuong.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/bunrieucua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:34","url":"/images/bunthitnuong.jpg"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:34","url":"/images/bunrieucua.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/chacalavong_thumb.jpg"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:34","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:34","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:41","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:58:41","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:42","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:58:42","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:43","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:58:43","url":"/api/foods?limit=50&offset=0"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:43","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:43","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:43","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:43","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:43","url":"/images/banhcan_thumb.jpg"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:43","url":"/images/banhdacua_thumb.jpg"}
{"duration":"40ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:43","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/banhkhot_thumb.jpg"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/banhxeo_thumb.jpg"}
{"duration":"38ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/bunrieucua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/bunthitnuong_thumb.jpg"}
{"duration":"44ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:44","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/canhchuacaloc_thumb.jpg"}
{"duration":"102ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:44","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"90ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/chagioPN_thumb.jpg"}
{"duration":"96ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/comtam_thumb.webp"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/ganuongmuoiot_thumb.png"}
{"duration":"47ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/goidudu_thumb.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/goingosen_thumb.jpg"}
{"duration":"47ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:45","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:45","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:56","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:58:56","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:57","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:58:57","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:57","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:58:57","url":"/api/foods?limit=50&offset=0"}
{"duration":"41ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:58:57","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/banhdacua_thumb.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/banhkhot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/banhxeo_thumb.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/cakhoto_thumb.jpg"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/chacalavong_thumb.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/comtam_thumb.webp"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/goidudu_thumb.jpg"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:58:58","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"9ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/tomchiengion_thumb.jpg"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:58:58","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:42","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:59:42","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:43","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:59:43","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:43","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 15:59:43","url":"/api/foods?limit=50&offset=0"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 15:59:43","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:43","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:43","url":"/images/banhcan_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:43","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"7ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:43","url":"/images/banhdacua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:43","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:44","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:44","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:44","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:44","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:44","url":"/images/banhkhot_thumb.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:44","url":"/images/banhxeo_thumb.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:44","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:45","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:45","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:45","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:45","url":"/images/bunbohue_thumb.png"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:45","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:45","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"8ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/chagioPN_thumb.jpg"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/comgahoian_thumb.jpeg"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/comtam_thumb.webp"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"4ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/goidudu_thumb.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/hutieunamvang_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"5ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/myquang_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"6ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/supcua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 15:59:46","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 15:59:46","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:00","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 16:00:00","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:01","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 16:00:01","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:01","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 16:00:01","url":"/api/foods?limit=50&offset=0"}
{"duration":"74ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 16:00:01","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:02","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:02","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:02","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:02","url":"/images/banhcan_thumb.jpg"}
{"duration":"33ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:02","url":"/images/banhdacua_thumb.jpg"}
{"duration":"37ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:02","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:02","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:02","url":"/images/banhkhot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:02","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:02","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:02","url":"/images/banhxeo_thumb.jpg"}
{"duration":"22ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:02","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/bunbohue_thumb.png"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/bunrieucua_thumb.jpg"}
{"duration":"41ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/bunthitnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/calocnuongtrui_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/canhchuacaloc_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/chagioPN_thumb.jpg"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/chaoluong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/comtam_thumb.webp"}
{"duration":"42ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/goidudu_thumb.jpg"}
{"duration":"28ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"16ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/hutieunamvang_thumb.webp"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/laucakeo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/laumam_thumb.webp"}
{"duration":"50ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/myquang_thumb.jpg"}
{"duration":"79ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:03","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/supcua_thumb.jpg"}
{"duration":"34ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:03","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:20","url":"/api/test","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"2ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 16:00:20","url":"/api/test"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:21","url":"/api/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"1ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 16:00:21","url":"/api/health"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:21","url":"/api/foods?limit=50&offset=0","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"dataLength":10982,"level":"info","message":"API response","method":"GET","service":"restaurant-api","statusCode":200,"timestamp":"2025-07-13 16:00:21","url":"/api/foods?limit=50&offset=0"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 16:00:21","url":"/api/foods?limit=50&offset=0"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/banhcan_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/banhdacua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/banhhoiheoquay_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"26ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/banhcan_thumb.jpg"}
{"duration":"35ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/banhdacua_thumb.jpg"}
{"duration":"41ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/banhhoiheoquay_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/banhkhot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/banhxeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/bolalot_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/banhkhot_thumb.jpg"}
{"duration":"25ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/banhxeo_thumb.jpg"}
{"duration":"31ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/bolalot_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/bunbohue_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/bunbohue_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/bunthitnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/bunrieucua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/bunthitnuong_thumb.jpg"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/bunrieucua_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/cakhoto_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"11ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/cakhoto_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/canhchuacaloc_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/calocnuongtrui_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/canhchuacaloc_thumb.jpg"}
{"duration":"23ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/calocnuongtrui_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/chacalavong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"10ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/chacalavong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/chaoluong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:22","url":"/images/chagioPN_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"13ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/chaoluong_thumb.jpg"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:22","url":"/images/chagioPN_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:23","url":"/images/comgahoian_thumb.jpeg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:23","url":"/images/comgahoian_thumb.jpeg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:23","url":"/images/comtam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:23","url":"/images/comtam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:23","url":"/images/ganuongmuoiot_thumb.png","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:23","url":"/images/ganuongmuoiot_thumb.png"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/goicuon_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"15ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/goicuon_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/goidudu_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"19ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/goidudu_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/goingosen_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"29ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/goingosen_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/hutieunamvang_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/laucakeo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/lauduoibo_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"41ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/hutieunamvang_thumb.webp"}
{"duration":"55ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/laucakeo_thumb.jpg"}
{"duration":"70ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/lauduoibo_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/laumam_thumb.webp","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/laumam_thumb.webp"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/myquang_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/nemnuong_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"20ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/myquang_thumb.jpg"}
{"duration":"32ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/nemnuong_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/ochapsa_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"18ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/ochapsa_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/supcua_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:24","url":"/images/tomchiengion_thumb.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"12ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/supcua_thumb.jpg"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:24","url":"/images/tomchiengion_thumb.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhcan_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhdacua_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhhoiheoquay_high.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"14ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:31","url":"/images/banhcan_high.jpg"}
{"duration":"17ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:31","url":"/images/banhdacua_high.jpg"}
{"duration":"24ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:31","url":"/images/banhhoiheoquay_high.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhcan_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhdacua_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhhoiheoquay_med.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:31","url":"/images/banhcan_med.jpg"}
{"duration":"48ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:31","url":"/images/banhdacua_med.jpg"}
{"duration":"66ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":404,"timestamp":"2025-07-13 16:00:31","url":"/images/banhhoiheoquay_med.jpg"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhcan.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhdacua.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"Request started","method":"GET","service":"restaurant-api","timestamp":"2025-07-13 16:00:31","url":"/images/banhhoiheoquay.jpg","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"duration":"21ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 16:00:31","url":"/images/banhcan.jpg"}
{"duration":"30ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 16:00:31","url":"/images/banhdacua.jpg"}
{"duration":"27ms","ip":"::1","level":"info","message":"Request completed","method":"GET","service":"restaurant-api","statusCode":304,"timestamp":"2025-07-13 16:00:31","url":"/images/banhhoiheoquay.jpg"}
